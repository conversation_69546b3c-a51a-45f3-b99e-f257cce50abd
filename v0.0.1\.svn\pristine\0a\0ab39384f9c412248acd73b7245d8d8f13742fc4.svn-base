﻿using Bluetooth;
using YSJ_20Cali;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Windows.Devices.Bluetooth;
using Windows.Devices.Bluetooth.GenericAttributeProfile;
using System.Configuration;
using System.Collections.Specialized;


namespace YSJ_20Cali
{

    public partial class Form1 : Form
    {
        /// <summary>
        /// 存储检测到的设备
        /// </summary>
        public HashSet<Windows.Devices.Bluetooth.BluetoothLEDevice> DeviceList = new HashSet<Windows.Devices.Bluetooth.BluetoothLEDevice>();
        public HashSet<DeviceService> deviceServices = new HashSet<DeviceService>();
        public HashSet<GattCharacteristic> writeCharacteristic = new HashSet<GattCharacteristic>();
        public HashSet<GattCharacteristic> notifyCharacteristics = new HashSet<GattCharacteristic>();
        BleCore bleCore = new BleCore();
        CaliData caliData = new CaliData();
        Guid CCCD = Guid.Parse("00002902-0000-1000-8000-00805f9b34fb");
        Guid RX_SERVICE_UUID = Guid.Parse("6e400001-b5a3-f393-e0a9-e50e24dcca9e");
        Guid RX_CHAR_UUID = Guid.Parse("6e400002-b5a3-f393-e0a9-e50e24dcca9e");
        Guid TX_CHAR_UUID = Guid.Parse("6e400003-b5a3-f393-e0a9-e50e24dcca9e");


        Guid DeviceInfomation = Guid.Parse("0000180a-0000-1000-8000-00805f9b34fb");
        Guid ManuFacture_UUID = Guid.Parse("00002a29-0000-1000-8000-00805f9b34fb");
        Guid ModeNumber_UUID = Guid.Parse("00002a24-0000-1000-8000-00805f9b34fb");
        Guid HardWareVer_UUID = Guid.Parse("00002a27-0000-1000-8000-00805f9b34fb");
        Guid FirmWareVer_UUID = Guid.Parse("00002a27-0000-1000-8000-00805f9b34fb");

        /*
                Guid RX_SERVICE_UUID0 = Guid.Parse("6e400004-b5a3-f393-e0a9-e50e24dcca9e");
                Guid RX_CHAR_UUID0 = Guid.Parse("6e400005-b5a3-f393-e0a9-e50e24dcca9e");
                Guid TX_CHAR_UUID0 = Guid.Parse("6e400006-b5a3-f393-e0a9-e50e24dcca9e");

                Guid RX_SERVICE_UUID1 = Guid.Parse("6e40000a-b5a3-f393-e0a9-e50e24dcca9e");
                Guid RX_CHAR_UUID1 = Guid.Parse("6e40000b-b5a3-f393-e0a9-e50e24dcca9e");
                Guid TX_CHAR_UUID1 = Guid.Parse("6e40000c-b5a3-f393-e0a9-e50e24dcca9e");
          */


        //private int count = 0; // 当前收到的包计数器
        //private int curState = STATE_IDLE;
        //private byte realtimehead_flag = 0;
        //private byte realtimefinish_flag = 0;
        //private byte[] realtimebuffer = new byte[100];//80
        //private byte[] realtimefinish_list = new byte[5];//4
        //private byte[] bufferData = new byte[HEART_RATE_COUNT];

        private const byte PCGETmeasureResult = 0x01;
        private const byte PCGETupdateMeasueWay = 0x03;
        private const byte PCGETansswerMeasureway = 0x06;          //answer set 
        private const byte PCGETdebugInfo = 0x07;
        private const byte PCGETlockStatus = 0x09;
        private const byte PCGETdlockStatus = 0x0b;
        private const byte PCGETreadmac = 0x0d;
        private const byte PCGETreadSn = 0x0f;
        private const byte PCGETsetSn = 0x10;
        private const byte PCGETcalResult = 0x13;
        private const byte PCGETcalInfo = 0x14;
        private const byte PCGETdecvName = 0x19;
        private const byte PCGETcalRadio = 0x1B;
        private const byte PCGETanswercalRadio = 0x1D;  //answer setCalRadio    
        private const byte PCGETdecVer = 0x21;
        private const byte PCGETCaliVal = 0x31;


        private const int PCSENDgetMeasureResult = 0x02;  //answer  message sendMeasureResult
        private const int PCSENDgetMeasureWay = 0x04;   //answe message getMeasureWay
        private const int PCSENDsetMessureWay = 0x05;
        private const int PCSENDgetLockStatus = 0x08;
        private const int PCSENDsetLockStatus = 0x0a;
        private const int PCSENDreadMac = 0x0c;
        private const int PCSENDreadSn = 0x0e;
        private const int PCSENDsendSn = 0x11;
        private const int PCSENDreadcCalResult = 0x12;
        private const int PCSENDdecvName = 0x18;
        private const int PCSENDreadCalRadio = 0x1A;
        private const int PCSENDsetCalRadio = 0x1C;

        private bool isSample = false;
        UInt32[] resultbyCalc = new UInt32[10];
        public Form1()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            this.Init();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        private void Init()
        {
            this.bleCore.MessAgeLog += BleCore_MessAgeLog;
            this.bleCore.DeviceScan += BleCore_DeviceScan;
            this.bleCore.DeviceFindService += BleCore_DeviceFindService;
            this.bleCore.ReceiveNotification += BleCore_ReceiveNotification;
            this.bleCore.DeviceConnectionStatus += BleCore_DeviceConnectionStatus;
        }
        private void GetStardDevVal()
        {
            caliData.calib_Points[0].y_value = (float)Properties.Settings.Default.C0val / 1000;
            caliData.calib_Points[1].y_value = (float)Properties.Settings.Default.C1val / 1000;
            caliData.calib_Points[2].y_value = (float)Properties.Settings.Default.C2val / 1000;
            caliData.calib_Points[3].y_value = (float)Properties.Settings.Default.C3val / 1000;
            caliData.calib_Points[4].y_value = (float)Properties.Settings.Default.C4val / 1000;
            caliData.calib_Points[5].y_value = (float)Properties.Settings.Default.C5val / 1000;
            caliData.calib_Points[6].y_value = (float)Properties.Settings.Default.C6val / 1000;
            caliData.calib_Points[7].y_value = (float)Properties.Settings.Default.C7val / 1000;
            caliData.calib_Points[8].y_value = (float)Properties.Settings.Default.C8val / 1000;
            caliData.calib_Points[9].y_value = (float)Properties.Settings.Default.C9val / 1000;
        }

        /// <summary>
        /// 异步线程
        /// </summary>
        /// <param name="action"></param>
        public static void RunAsync(Action action)
        {
            ((Action)(delegate ()
            {
                action.Invoke();
            })).BeginInvoke(null, null);
        }

        /// <summary>
        /// 蓝牙状态事件
        /// </summary>
        /// <param name="status"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_DeviceConnectionStatus(BluetoothConnectionStatus status)
        {
            if (status == BluetoothConnectionStatus.Disconnected)
            {
                deviceServices.Clear();
                writeCharacteristic.Clear();
                notifyCharacteristics.Clear();
            }
        }

        /// <summary>
        /// 接收通知事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="data"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_ReceiveNotification(GattCharacteristic sender, byte[] data)
        {

            // txtLog.AppendText("\r\n" + sender.Uuid.ToString() + "\r\n");
            //string str = System.Text.Encoding.UTF8.GetString(data) + "\r\n";// + BitConverter.ToString(data)+"\r\n";
            // txtLog.AppendText(str);          
            realtimeprocess(data);

        }


        private void realtimeprocess(byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                return;
            }
            //Log.d(TAG, "current state = " + curState);
            // 输出收到的字节码
            int tmp = 0;
            int len = 0;
            tmp = data[0];
            len = data[1];
            const int dataStart = 2;
            string mystr = "";
            txtLog.AppendText("Got BLE CMD" + tmp.ToString() + "\r\n");
            switch (tmp)
            {
                case PCGETmeasureResult:
                    if (0 == data[dataStart])
                    {
                        // mystr += "1-OK!";
                        if (1 == data[dataStart + 1])
                        {
                            UInt32 a, b, c, d, e, j;
                            a = (UInt32)((data[dataStart + 3] << 8) + data[dataStart + 2]);
                            b = (UInt32)((data[dataStart + 5] << 8) + data[dataStart + 4]);
                            c = (UInt32)((data[dataStart + 7] << 8) + data[dataStart + 6]);
                            j = (UInt32)((data[dataStart + 9] << 8) + data[dataStart + 8]);
                            if ((j % 10) > 5)
                            {
                                d = (j / 10 + 1) / 100;
                                e = (j / 10 + 1) % 100;
                            }
                            else
                            {
                                d = (j / 10) / 100;
                                e = (j / 10) % 100;
                            }
                            mystr += "  D:" + a.ToString() + "   G:" + b.ToString() + "   B:" + c.ToString() + "   R:" + d.ToString() + "." + e.ToString() + " mgDL";
                        }
                        else
                        {
                            mystr += "2-ERR! ";
                        }
                    }
                    else
                    {
                        mystr += "1-ERROR！";
                    }

                    break;
                case PCGETupdateMeasueWay:
                    break;
                case PCGETansswerMeasureway:
                    break;        //answer set 
                case PCGETdebugInfo:
                    break;
                case PCGETlockStatus:
                    if (1 == data[dataStart])
                    {
                        lbLock.Text = "锁定";
                        lbLock.BackColor = Color.Red;
                    }
                    else
                    {
                        lbLock.Text = "开锁";
                        lbLock.BackColor = Color.Green;
                    }
                    break;
                case PCGETdlockStatus:
                    if (1 == data[dataStart])
                    {
                        lbLock.Text = "锁定";
                        lbLock.ForeColor = Color.Red;
                    }
                    else
                    {
                        lbLock.Text = "开锁";
                        lbLock.ForeColor = Color.Green;
                    }
                    break;
                case PCGETreadmac:
                    byte[] mybytemacarray = new byte[6];
                    string xxstring = string.Empty;
                    for (uint i = 0; i < 6; i++)
                    {
                        mybytemacarray[i] = data[dataStart + i];
                        // xxstring += Convert.ToInt32(mybytemacarray[i]).ToString;
                        xxstring += mybytemacarray[i].ToString("X2") + ":";
                    }

                    lbMac.Text = xxstring.Substring(0, xxstring.Length - 1);
                    break;
                case PCGETreadSn:
                    byte[] mybytesnarray = new byte[20];
                    for (uint i = 0; i < 20; i++)
                    {
                        mybytesnarray[i] = data[dataStart + i];
                    }
                    txtSN.Text = System.Text.Encoding.UTF8.GetString(mybytesnarray);
                    break;
                case PCGETsetSn:

                    break;
                case PCGETcalResult:
                    break;
                case PCGETcalInfo:
                    break;
                case PCGETdecvName:
                    break;
                case PCGETcalRadio:
                    break;
                case PCGETanswercalRadio:
                    break; //answer setCalRadio    
                case PCGETdecVer:
                    byte[] mybytesnarrayx = new byte[60];
                    for (uint i = 0; i < data.Length-2; i++)
                    {
                        mybytesnarrayx[i] = data[dataStart + i];
                    }
                    lbFirmwareVer.Text = System.Text.Encoding.UTF8.GetString(mybytesnarrayx);
                    break;
                case PCGETCaliVal:
                    RecvCalVal(data);
                    break;
                default:
                    break;
            }
            txtLog.AppendText(mystr + "\r\n");

        }


        /// <summary>
        /// 获取服务事件
        /// </summary>
        /// <param name="deviceService"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_DeviceFindService(DeviceService deviceService)
        {
            deviceServices.Add(deviceService);
 //           int x = 0xFF;
        }

        /// <summary>
        /// 搜索蓝牙事件
        /// </summary>
        /// <param name="bluetoothLEDevice"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_DeviceScan(BluetoothLEDevice bluetoothLEDevice)
        {
            RunAsync(() =>
            {
                byte[] _Bytes1 = BitConverter.GetBytes(bluetoothLEDevice.BluetoothAddress);
                Array.Reverse(_Bytes1);
                this.DeviceList.Add(bluetoothLEDevice);
            });
        }

        /// <summary>
        /// 提示信息事件
        /// </summary>
        /// <param name="type"></param>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_MessAgeLog(MsgType type, string message, byte[] data = null)
        {
            RunAsync(() =>
            {
                if (message != string.Empty)
                {
                    
                    if (message == "Connected")
                    {
                        //btnConn.Text = "断开";
                        Thread.Sleep(2000);
                        //bleCore.BlueConnectStatus = false;
                        StartRecv();
                        Thread.Sleep(200);
                        ReadMac();
                        Thread.Sleep(200);
                        ReadDevSn();
                        Thread.Sleep(200);
                        ReadDevLockStatus();
                        Thread.Sleep(200);
                        ReadDevVer();


                    }
                    else if(message ==  "DisConnect")
                    {
                        btnConn.Text = "连接";
                    }
                
                    if (message.Contains("YSJ-20"))
                    {
                        this.txtDevName.Text = message.Substring(5, 12);
                        this.txtLog.AppendText("设备名" + this.txtDevName.Text + "\r\n");
                    }
                    this.txtLog.AppendText(DateTime.Now.ToString("HH:mm:ss.fff", DateTimeFormatInfo.InvariantInfo) + ": " + message + "\r\n");
                }
            });
        }

        /// <summary>
        /// 扫描时间
        /// </summary>
        public int bleScanCount = 0;
        /// <summary>
        /// 按钮触发扫描事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnScan_Click(object sender, EventArgs e)
        {
            if (this.btnScan.Text == "扫描蓝牙")
            {
                this.txtLog.Clear();
                this.bleCore.StartBleDeviceWatcher();
                this.btnScan.Text = "停止扫描";
                this.bleScanCount = 15;
                timerBleScan.Enabled = true;
            }
            else
            {
                timerBleScan.Enabled = false;
                this.bleCore.StopBleDeviceWatcher();
                this.btnScan.Text = "扫描蓝牙";
            }
        }

        /// <summary>
        /// 扫描时间
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void timerBleScan_Tick(object sender, EventArgs e)
        {
            this.btnScan.Text = "停止扫描(" + bleScanCount + ")";
            if (bleScanCount-- == 0)
            {
                this.bleCore.StopBleDeviceWatcher();
                this.btnScan.Text = "扫描蓝牙";
                timerBleScan.Enabled = false;
            }
        }

        /// <summary>
        /// 连接蓝牙事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConn_Click(object sender, EventArgs e)
        {
            string mac = txtDevName.Text.Trim();
            BluetoothLEDevice device = DeviceList.Where(c => c.Name == mac)?.FirstOrDefault();
            if (bleCore.BlueConnectStatus == false)
            {

                if (string.IsNullOrEmpty(mac))
                {
                    MessageBox.Show("请输入蓝牙MAC地址");
                    return;
                }


                if (device != null)
                {
                    bleCore.CurrentDevice?.Dispose();
                    deviceServices.Clear();
                    notifyCharacteristics.Clear();
                    writeCharacteristic.Clear();
                    bleCore.StartMatching(device);
                }
                else
                {
                    MessageBox.Show("没有发现此蓝牙，请重新搜索.");
                    //this.btnServer.Enabled = false;
                }
            }
            else
            {
                bleCore.Dispose();
                btnConn.Text = "连接";
                bleCore.BlueConnectStatus = false;
                txtDevName.Text = string.Empty;
            }

        }

        private async void StartRecv()
        {
            foreach (var device in deviceServices)
            {
                if (device.gattCharacteristic == null || device.gattCharacteristic.Count < 1)
                {
                    continue;
                }
                if (device.gattDeviceService.Uuid != RX_SERVICE_UUID)
                {
                    continue;
                }
                foreach (var notifyCharacteristic in device.gattCharacteristic)
                {
                    await bleCore.SetNotify(notifyCharacteristic);
                }
            }
        }
        private async void btnNotify_Click(object sender, EventArgs e)
        {

            StartRecv();


        }


        private async void button1_Click(object sender, EventArgs e)
        {


            float k;
            float b;
            k = (float)0.9709;
            b = (float)-2.181;
           // k = caliData.k_val;
           // b = caliData.b_val;
            Sendkb(k,b);
            //  k = Properties.Settings.Default.corrcoef_val;
            //   this.txtLog.AppendText(" corrcoef_val:" + k.ToString());
            //caliData.calib_Points[0].x_value = 3.37f;
            //caliData.calib_Points[1].x_value = 5.27f;
            //caliData.calib_Points[2].x_value = 7.70f;
            //caliData.calib_Points[3].x_value = 10.53f;
            //caliData.calib_Points[4].x_value = 15.57f;
            //caliData.calib_Points[5].x_value = 16.07f;
            //caliData.calib_Points[6].x_value = 18.57f;
            //caliData.calib_Points[7].x_value = 24.47f;
            //caliData.calib_Points[8].x_value = 29.80f;
            //caliData.calib_Points[9].x_value = 30.83f;

            //caliData.calib_Points[0].y_value = 1.10f;
            //caliData.calib_Points[1].y_value = 2.37f;
            //caliData.calib_Points[2].y_value = 4.60f;
            //caliData.calib_Points[3].y_value = 9.37f;
            //caliData.calib_Points[4].y_value = 12.67f;
            //caliData.calib_Points[5].y_value = 13.17f;
            //caliData.calib_Points[6].y_value = 16.83f;
            //caliData.calib_Points[7].y_value = 21.53f;
            //caliData.calib_Points[8].y_value = 26.20f;
            //caliData.calib_Points[9].y_value = 27.82f;

            //caliData.CalibLinear(10);
            //this.txtLog.AppendText("  b_val:" + caliData.b_val.ToString());
            //this.txtLog.AppendText("  k_val:"+ caliData.k_val.ToString());
            //this.txtLog.AppendText("  corrcoef_val:"+ caliData.corrcoef_val.ToString());
            // string str = Properties.Settings.Default.SC0val.ToString();
            //str += Properties.Settings.Default.SC1val.ToString();
            // this.txtLog.AppendText(str);
            // Properties.Settings.Default.SC0val = 4000;
            //Properties.Settings.Default.Save();
            /*
            // config read
            string str = ConfigurationManager.AppSettings.Get("key1");
            str += ConfigurationManager.AppSettings.Get("key2");
            this.txtLog.AppendText(str);
            //config write
            string file = System.Windows.Forms.Application.ExecutablePath;
            Configuration config = ConfigurationManager.OpenExeConfiguration(file);
            config.AppSettings.Settings["key1"].Value = "DemoValue";
            config.Save(ConfigurationSaveMode.Modified);
            ConfigurationManager.RefreshSection("appSettings");
            */
        }
        private void SendDataToBLE(byte[] xdata)
        {
            /*
            //byte[] topArray = new byte[4];
            byte[] tsendData = new byte[1200];
            int len = xdata.Length;
            if(len>1100)
            {
                return;
            }
            tsendData[0] = 0x55;
            tsendData[1] = 0xAA;
            tsendData[2] = (byte)((xdata.Length + 6) & 0xff);
            tsendData[3] = (byte)((xdata.Length + 6)>>8);
       
            UInt16 sum = 0;
            for (UInt16 i = 0; i < len + 4; i++)
            {
                sum += (byte)tsendData[i];
            }
            tsendData[len + 4] = ((byte)(sum & 0xff));
            tsendData[len + 5] = ((byte)(sum >> 8));
            byte[] sendData = new byte[len + 6];
            for (UInt16 i = 0; i < len + 6; i++)
            {
                sendData[i] = tsendData[i];
            }
            */
            // tsendData.CopyTo(sendData)
            //  WriteData(sendData);

        }
        private void GetDeviceStatus()
        {

            // WriteData(new byte[] { 0x18,0x00 });
            byte[] myArray = { 0x18, 0x00 };
            SendDataToBLE(myArray);
        }
        private void btnGetData_Click(object sender, EventArgs e)
        {
            /*
            if (this.btnGetData.Text == "采集数据(开)")
            {

                this.btnGetData.Text = "采集数据(关)";
                WriteData(new byte[] { 0x01 });
            }
            else
            {
                this.btnGetData.Text = "采集数据(开)";
            }
            */
            //   GetDeviceStatus();
        }

        private async void WriteData(byte[] data)
        {
            try
            {
                foreach (var device in deviceServices)
                {
                    if (device.gattDeviceService.Uuid != RX_SERVICE_UUID)
                    {
                        continue;
                    }
                    var serviceUuid = device.gattDeviceService.Uuid;
                    if (device.gattCharacteristic == null || device.gattCharacteristic.Count < 1)
                    {
                        continue;
                    }
                    foreach (var writeCharacteristic in device.gattCharacteristic)
                    {
                        if (writeCharacteristic.Uuid != RX_CHAR_UUID)
                        {
                            continue;
                        }
                        await bleCore.Write(writeCharacteristic, data);
                    }
                }
            }
            catch (Exception e)
            {
                e.ToString();
            }
        }
        

        public byte[] intToBytes(int value)
        {
            byte[] src = new byte[4];
            src[3] = (byte)((value >> 24) & 0xFF);
            src[2] = (byte)((value >> 16) & 0xFF);
            src[1] = (byte)((value >> 8) & 0xFF);//高8位
            src[0] = (byte)(value & 0xFF);//低位
            return src;
        }


        private void ReadDevLockStatus()
        {
            byte[] start2a = new byte[] { (byte)0x08, (byte)00 };
            WriteData(start2a);
        }
     
        private void DevLock()
        {
            byte[] start2a = new byte[] { (byte)0x0A, (byte)0x01, (byte)0x01 };
            WriteData(start2a);
        }
        private void btnLock_Click(object sender, EventArgs e)
        {
            DevLock();
        }
        private void DevUnLock()
        {
            byte[] start2a = new byte[] { (byte)0x0A, (byte)0x01, (byte)0x00 };
            WriteData(start2a);
        }
        private void btnUnlock_Click(object sender, EventArgs e)
        {
            DevUnLock();
        }
        private void ReadMac()
        {
            byte[] start2a = new byte[] { (byte)0x0C,(byte)0x00 };
            WriteData(start2a);
        }
        private void ReadDevVer()
        {
            byte[] start2a = new byte[] { (byte)0x20, (byte)0x00 };
            WriteData(start2a);
        }
        private void ReadDevSn()
        {
            byte[] start2a = new byte[] { (byte)0x0E, (byte)0x00 };
            WriteData(start2a);
        }
        private void WriteDevSn()
        {
            byte[] snpreBytes = System.Text.Encoding.UTF8.GetBytes(txtSN.Text);
            byte[] snBytes = new byte[22];
            snBytes[0] = 0x10;
            snBytes[1] = 0x14;
            for (uint i = 0; i < snpreBytes.Length; i++)
            {
                snBytes[2 + i] = snpreBytes[i];
            }
            WriteData(snBytes);
        }
        private void btnSN_Click(object sender, EventArgs e)  //写SN
        {
            WriteDevSn();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            ReadDevSn();
        }
        private void CalGetVal(Int32 channal)
        {
            byte[] mybyte = new byte[] { (byte)0x30, (byte)0x01, (byte)0x01 };
            mybyte[2] = (byte)channal;
            WriteData(mybyte);
        }
        private void Sendkb(float k,float b)
        {
            byte[] mybyte = new byte[] { (byte)0x32, (byte)0x06, (byte)0x00,(byte)0x01, (byte)0x02, (byte)0x03,(byte)0x04, (byte)0x05 };
            UInt32 aa, bb;
            if (k >= 0)
            {
                mybyte[2] = 1;
            }
            else
            {
                mybyte[2] = 0;
            }
            aa = (UInt32)(Math.Abs(k * 10000) );
            mybyte[3] = (byte)(aa >> 8);
            mybyte[4] = (byte)(aa & 0xff);
            if (b >= 0)
            {
                 mybyte[5] = 1;
            }
            else
            {
                 mybyte[5] = 0;
            }
            bb = (UInt32)(Math.Abs(b * 10000) ); //0x5532
            mybyte[6] = (byte)(bb >> 8); //0x55    85
            mybyte[7] = (byte)(bb & 0xff); //0x32   
            WriteData(mybyte);
            Thread.Sleep(1000);
            WriteData(mybyte);
        }
        private void RecvCalVal(byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                return;
            }
            //Log.d(TAG, "current state = " + curState);
            // 输出收到的字节码
            UInt32 tmp = 0;
            UInt32 len = 0;
            UInt32 j, d, e;
            UInt32[] array_a = new UInt32[12];
            UInt32[] array_b = new UInt32[12];
            UInt32[] array_c = new UInt32[12];
            bool[] arraybool = new bool[12];
            tmp = data[0];
            len = data[1];
            int dataStart = 2;
            tmp = data[4];
            string mystr = " ";
            if (1 == data[dataStart+1])
            {
                // mystr += "1-OK!";
                
                    dataStart = 3;
                    UInt32 a, b, c;
                    a = (UInt32)((data[dataStart + 3] << 8) + data[dataStart + 2]);               
                    b = (UInt32)((data[dataStart + 5] << 8) + data[dataStart + 4]);                    
                    c = (UInt32)((data[dataStart + 7] << 8) + data[dataStart + 6]);                   
                    j = (UInt32)((data[dataStart + 9] << 8) + data[dataStart + 8]);
                    if(isSample)//为真是参考样机采样，假时是收集校准通道数据
                    {
                        switch (tmp)
                        {
                            case 0:
                                Properties.Settings.Default.C0val = j;
                                break;
                            case 1:
                                Properties.Settings.Default.C1val = j;
                                break;
                            case 2:
                                Properties.Settings.Default.C2val = j;
                                break;
                            case 3:
                                Properties.Settings.Default.C3val = j;
                                break;
                            case 4:
                                Properties.Settings.Default.C4val = j;
                                break;
                            case 5:
                                Properties.Settings.Default.C5val = j;
                                break;
                            case 6:
                                Properties.Settings.Default.C6val = j;
                                break;
                            case 7:
                                Properties.Settings.Default.C7val = j;
                                break;
                            case 8:
                                Properties.Settings.Default.C8val = j;
                                break;
                            case 9:
                                Properties.Settings.Default.C9val = j;
                                break;

                            default:
                                break;
                        }
                        Properties.Settings.Default.Save();
                    }
                    else
                    {
                    if (tmp < 10)
                    {
                        caliData.result[tmp] = j;
                        caliData.datagotten[tmp] = true;
                        if (tmp == 9)
                        {
                            
                        }
                    }

                }                  
                    if ((j % 10) > 5)
                    {
                        d = (j / 10 + 1) / 100;
                        e = (j / 10 + 1) % 100;
                    }
                    else
                    {
                        d = (j / 10) / 100;
                        e = (j / 10) % 100;
                    }
                mystr = "色板";
                mystr += "CH:" + (data[4]+1).ToString() + "   D:" + a.ToString() + "   G:" + b.ToString() + "   B:" + c.ToString() + "   R:" + d.ToString() + "." + e.ToString() + " mgDL";
                  
            }
            else
            {
                mystr += "2-ERR! ";
            }

            txtLog.AppendText(mystr + "\r\n");

            
        }
        private void txtTextChange(object sender, EventArgs e)
        {
            bleCore.BluetoothSignalStress = Convert.ToInt32(txtSignStress.Text);
            Properties.Settings.Default.signalstress = txtSignStress.Text;
        }

        private void FormLoad(object sender, EventArgs e)
        {
            bleCore.BluetoothSignalStress = Convert.ToInt32(txtSignStress.Text);
            txtSignStress.Text = Properties.Settings.Default.signalstress;
            GetStardDevVal();
        }

        private void askCali()
        {
            byte[] mybyte = new byte[] { (byte)0x16, (byte)0x01, (byte)0x02 };
            WriteData(mybyte);
        }
        private void step1CalVal(byte[] data)
        {

        }
        private void btnWhite_Click(object sender, EventArgs e)
        {
            askCali();
        }
        private void btnNothing_Click(object sender, EventArgs e)
        {
           // CalGetVal(11);
        }
        private void btnColor1_Click(object sender, EventArgs e)
        {
            CalGetVal(0);
            btnCol1.BackColor = Color.Blue;
        }

        private void btnCol2_Click(object sender, EventArgs e)
        {
            CalGetVal(1);
            btnCol2.BackColor = Color.Blue;
        }

        private void btnCol3_Click(object sender, EventArgs e)
        {
            CalGetVal(2);
            btnCol3.BackColor = Color.Blue;
        }

        private void btnCol4_Click(object sender, EventArgs e)
        {
            CalGetVal(3);
            btnCol4.BackColor = Color.Blue;
        }

        private void btnCol5_Click(object sender, EventArgs e)
        {
            CalGetVal(4);
            btnCol5.BackColor = Color.Blue;
        }

        private void btnCol6_Click(object sender, EventArgs e)
        {
            CalGetVal(5);
            btnCol6.BackColor = Color.Blue;
        }

        private void btn7_Click(object sender, EventArgs e)
        {
            CalGetVal(6);
            btnCol7.BackColor = Color.Blue;
        }

        private void btn8_Click(object sender, EventArgs e)
        {
            CalGetVal(7);
            btnCol8.BackColor = Color.Blue;
        }

        private void btnCol9_Click(object sender, EventArgs e)
        {
            CalGetVal(8);
            btnCol9.BackColor = Color.Blue;
        }

        private void btnCol10_Click(object sender, EventArgs e)
        {
            CalGetVal(9);
            btnCol10.BackColor = Color.Blue;
        }

       

        private void btnReadData_Click(object sender, EventArgs e)
        {
            StartRecv();
            Thread.Sleep(200);
            ReadMac();
            Thread.Sleep(200);
            ReadDevSn();
            Thread.Sleep(200);
            ReadDevLockStatus();
            Thread.Sleep(200);
            ReadDevVer();
        }

        private void btnSampGet_Click(object sender, EventArgs e)
        {
            if(isSample)
            {
                isSample = false;
                btnSampGet.BackColor = SystemColors.Control;
                btnSampGet.UseVisualStyleBackColor = true;
                

            }
            else
            {
                isSample = true;
                btnSampGet.BackColor = Color.Red;
            }
        }

        private void btnGetArmValue_Click(object sender, EventArgs e)
        {
            string mystr = " ";
           
            mystr += caliData.calib_Points[0].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[1].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[2].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[3].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[4].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[5].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[6].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[7].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[8].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[9].y_value.ToString("f2");
            txtLog.AppendText(mystr + "\r\n");
        }

        private void btnCali_Click(object sender, EventArgs e)
        {
            btnCol1.BackColor = SystemColors.Control;
            btnCol1.UseVisualStyleBackColor = true;

            btnCol2.BackColor = SystemColors.Control;
            btnCol2.UseVisualStyleBackColor = true;

            btnCol3.BackColor = SystemColors.Control;
            btnCol3.UseVisualStyleBackColor = true;

            btnCol4.BackColor = SystemColors.Control;
            btnCol4.UseVisualStyleBackColor = true;

            btnCol5.BackColor = SystemColors.Control;
            btnCol5.UseVisualStyleBackColor = true;

            btnCol6.BackColor = SystemColors.Control;
            btnCol6.UseVisualStyleBackColor = true;

            btnCol7.BackColor = SystemColors.Control;
            btnCol7.UseVisualStyleBackColor = true;

            btnCol8.BackColor = SystemColors.Control;
            btnCol8.UseVisualStyleBackColor = true;

            btnCol9.BackColor = SystemColors.Control;
            btnCol9.UseVisualStyleBackColor = true;

            btnCol10.BackColor = SystemColors.Control;
            btnCol10.UseVisualStyleBackColor = true;

            if (caliData.datagotten[9])
            {
                for (UInt32 i = 0; i < 10; i++)
                {
                    resultbyCalc[i] = caliData.result[i];
                    caliData.calib_Points[i].x_value = (float)caliData.result[i] / 1000;
                   
                }

                caliData.CalibLinear(10);
                txtLog.AppendText("k值：" + caliData.k_val.ToString() + " b值" + caliData.b_val.ToString()+"可信度" + caliData.corrcoef_val.ToString()+ "\r\n");
                if (caliData.corrcoef_val > Properties.Settings.Default.corrcoef_val)
                {
                    Sendkb(caliData.k_val, caliData.b_val);
                }
                else
                {
                    MessageBox.Show("校准失败，请重新校准！", "请重新校准");
                }
            }
            
            for (UInt32 i = 0; i < 10; i++)
            {
                caliData.result[i] = 0;
                caliData.datagotten[i] = false;
            }
        }

        private void btnStartCali_Click(object sender, EventArgs e)
        {
            byte[] mybyte = new byte[] { (byte)0x33, (byte)0x01, (byte)0x01 };
            mybyte[2] = 00;
            WriteData(mybyte);
            btnCol1.BackColor = SystemColors.Control;
            btnCol1.UseVisualStyleBackColor = true;

            btnCol2.BackColor = SystemColors.Control;
            btnCol2.UseVisualStyleBackColor = true;

            btnCol3.BackColor = SystemColors.Control;
            btnCol3.UseVisualStyleBackColor = true;

            btnCol4.BackColor = SystemColors.Control;
            btnCol4.UseVisualStyleBackColor = true;

            btnCol5.BackColor = SystemColors.Control;
            btnCol5.UseVisualStyleBackColor = true;

            btnCol6.BackColor = SystemColors.Control;
            btnCol6.UseVisualStyleBackColor = true;

            btnCol7.BackColor = SystemColors.Control;
            btnCol7.UseVisualStyleBackColor = true;

            btnCol8.BackColor = SystemColors.Control;
            btnCol8.UseVisualStyleBackColor = true;

            btnCol9.BackColor = SystemColors.Control;
            btnCol9.UseVisualStyleBackColor = true;

            btnCol10.BackColor = SystemColors.Control;
            btnCol10.UseVisualStyleBackColor = true;
        }
        //float[] farraya = { 1.10f, 2.37f, 4.50f, 9.37f, 12.90f, 12.90f, 16.83f, 21.53f, 26.20f, 27.47f };
      //  float[] farrayb = { -0.27f, 1.62f,   4.45f,   9.17f,   12.95f ,  -12.95f ,  16.73f ,  21.45f ,  26.17f ,  26.17f };
        private void btnCheck_Click(object sender, EventArgs e)
        {
            float a = 0.0f;
            bool isblank = false;
            //for (UInt32 i = 0; i < 10; i++)
            //{
            //    resultbyCalc[i] = (UInt32)(farraya[i] * 1000f);
            //}
            //caliData.k_val = 0.9439988f;
            //caliData.b_val = -2.154701f;
            string str ="";
            float[] tmpArray = new float[10];
            a = (resultbyCalc[0] / 1000) * caliData.k_val + caliData.b_val;
            if (a > 0)
            {
  
                    str = " ";
               
            }
            for (UInt32 i = 0; i < 10; i++)
            {
               
                a = (resultbyCalc[i]/1000) * caliData.k_val + caliData.b_val;
                tmpArray[i] = a -  caliData.calib_Points[i].y_value ;              
                if (a > 0)
                {
                   if(isblank )
                   { 
                        str += " "+ a.ToString("f2") + "   ";
                        isblank = false;
                    }
                   else
                    {
                        if (a >= 10)
                        {
                            str += a.ToString("f2") + "    ";
                        }
                        else
                        {
                            str += a.ToString("f2") + "   ";
                        }
                    }
                    
                }
                else
                {
                    isblank = true;
                    str += a.ToString("f2") + "  ";
                }
            }
            txtLog.AppendText(str + "\r\n");
            str = "";
            if (tmpArray[0] > 0)
            {
                str = " ";
            }
            for (UInt32 i =0;i<10;i++)
            {               
                if (tmpArray[i] > 0)
                {
                    if (isblank)
                    {
                        str += " " + tmpArray[i].ToString("f2") + "   ";
                        isblank = false;
                    }
                }
                else
                {
                    if (tmpArray[i] >= 10)
                    {
                        str += tmpArray[i].ToString("f2") + "   ";
                    }
                    else
                    {
                        isblank = true; 
                        str += tmpArray[i].ToString("f2") + "   ";
                    }

                }
            }
            txtLog.AppendText(str + "\r\n");

        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            //string mystr = string.Empty;
            //if(farraya[0]>0)
            //{
            //    mystr = " ";
            //}
            //for (UInt32 i = 0; i < 10; i++)
            //{
               
            //    if (farraya[i] > 0)
            //    {

            //        mystr += farraya[i].ToString("f2") + "   ";
            //    }
            //    else
            //    {
            //        mystr += farraya[i].ToString("f2") + "   ";
            //    }
            //}
            //txtLog.AppendText(mystr + "\r\n");
            //mystr = string.Empty;
            //if (farrayb[0] > 0)
            //{
            //    mystr = " ";
            //}
            //for (UInt32 i = 0; i < 10; i++)
            //{

            //    if (farrayb[i] > 0)
            //    {

            //        mystr += farrayb[i].ToString("f2") + "   ";
            //    }
            //    else
            //    {
            //        mystr += farrayb[i].ToString("f2") + "   ";
            //    }
            //}
            //txtLog.AppendText(mystr + "\r\n");
        }
    }
}
