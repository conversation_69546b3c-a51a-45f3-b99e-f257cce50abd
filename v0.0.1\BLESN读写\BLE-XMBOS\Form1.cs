﻿//#define GETSKINDATA
using Bluetooth;
//using OfficeOpenXml;
//using OfficeOpenXml.Drawing.Slicer.Style;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.Remoting.Channels;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Windows.ApplicationModel;
using Windows.ApplicationModel.VoiceCommands;
using Windows.Devices.Bluetooth;
using Windows.Devices.Bluetooth.GenericAttributeProfile;
using Windows.UI.Xaml.Documents;
using YSJ_10Cali;


namespace YSJ_10Cali
{

    public partial class Form1 : Form
    {


        /// <summary>
        /// 存储检测到的设备
        /// </summary>
        public HashSet<Windows.Devices.Bluetooth.BluetoothLEDevice> DeviceList = new HashSet<Windows.Devices.Bluetooth.BluetoothLEDevice>();
        public HashSet<DeviceService> deviceServices = new HashSet<DeviceService>();
        public HashSet<GattCharacteristic> writeCharacteristic = new HashSet<GattCharacteristic>();
        public HashSet<GattCharacteristic> notifyCharacteristics = new HashSet<GattCharacteristic>();
        BleCore bleCore = new BleCore();
        CaliData caliData = new CaliData();
        Guid CCCD = Guid.Parse("00002902-0000-1000-8000-00805f9b34fb");
        Guid RX_SERVICE_UUID = Guid.Parse("6e400001-b5a3-f393-e0a9-e50e24dcca9e");
        Guid RX_CHAR_UUID = Guid.Parse("6e400002-b5a3-f393-e0a9-e50e24dcca9e");
        Guid TX_CHAR_UUID = Guid.Parse("6e400003-b5a3-f393-e0a9-e50e24dcca9e");


        Guid DeviceInfomation = Guid.Parse("0000180a-0000-1000-8000-00805f9b34fb");
        Guid ManuFacture_UUID = Guid.Parse("00002a29-0000-1000-8000-00805f9b34fb");
        Guid ModeNumber_UUID = Guid.Parse("00002a24-0000-1000-8000-00805f9b34fb");
        Guid HardWareVer_UUID = Guid.Parse("00002a27-0000-1000-8000-00805f9b34fb");
        Guid FirmWareVer_UUID = Guid.Parse("00002a27-0000-1000-8000-00805f9b34fb");

        private const byte PCGETmeasureResult = 0x01;
        private const byte PCGETupdateMeasueWay = 0x03;
        private const byte PCGETansswerMeasureway = 0x06;          //answer set 
        private const byte PCGETdebugInfo = 0x07;
        private const byte PCGETlockStatus = 0x09;
        private const byte PCSETlockStatus = 0x0b;
        private const byte PCGETreadmac = 0x0d;
        private const byte PCGETreadSn = 0x0f;
        private const byte PCGETsetSn = 0x10;
        private const byte PCGETcalResult = 0x13;
        private const byte PCGETcalInfo = 0x14;
        private const byte PCGETdecvName = 0x19;
        private const byte PCGETcalRadio = 0x1B;
        private const byte PCGETanswercalRadio = 0x1D;  //answer setCalRadio    
        private const byte PCGETdecVer = 0x21;
        private const byte PCGETCaliVal = 0x31;
        private const byte PCGETE01Msg = 0x45;
        private const byte PCSETAutolockStatus = 0x4D;
        private const byte PCGETTestVal = 0x4F;
        private const byte PCGETAutoTestVal = 0x51;

        private const int PCSENDgetMeasureResult = 0x02;  //answer  message sendMeasureResult
        private const int PCSENDgetMeasureWay = 0x04;   //answe message getMeasureWay
        private const int PCSENDsetMessureWay = 0x05;
        private const int PCSENDgetLockStatus = 0x08;
        private const int PCSENDsetLockStatus = 0x0a;
        private const int PCSENDreadMac = 0x0c;
        private const int PCSENDreadSn = 0x0e;
        private const int PCSENDsendSn = 0x11;
        private const int PCSENDreadcCalResult = 0x12;
        private const int PCSENDdecvName = 0x18;
        private const int PCSENDreadCalRadio = 0x1A;
        private const int PCSENDsetCalRadio = 0x1C;
        private const int NOSTARD = 0;      //0:正常产品，解锁发送时间为2038        
                                            //1:1个月后自动加锁
        static UInt32 timetest = 0;
        static UInt32 deviceunit = 0;

        //    timestamp mytimestamp = new timestamp();

        private bool isSample = false;
        UInt32[] resultbyCalc = new UInt32[10];
        string fileName = string.Empty;
        string TestFileName = string.Empty;
        string excelName = string.Empty;
        string TestExcelName = string.Empty;
        string CALExcelName = string.Empty;
        
        string errorString = string.Empty;
        UInt32 resultforratio = 0;
        UInt32 preratio = 0;
        public Form1()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            this.Init();
            
            // 在窗体加载完成后设置权限
            this.Load += Form1_Load;
        }
        
        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始化状态栏默认状态（在设置用户信息之前）
                InitializeStatusBar();
                
            // 确保所有控件都已初始化后再设置权限
            this.SetupRoleBasedUI();
                
                // 重置锁按钮文本为空，这样状态栏会显示"加锁状态：无"
                if (btnLock != null)
                {
                    btnLock.Text = "";
                }
                
                // 初始化锁按钮颜色（但不更新状态栏，保持默认的"无"状态）
                UpdateLockButtonColor();
                // 移除这行：UpdateStatusBarLockStatus();
                
                // 初始化自动上锁天数文本框
                if (txtAutoLockDays != null)
                {
                    txtAutoLockDays.Text = "1";
                }
                else
                {
                    Console.WriteLine("警告：txtAutoLockDays 控件未找到");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Form1_Load 出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 初始化状态栏
        /// </summary>
        private void InitializeStatusBar()
        {
            try
            {
                if (statusLabelUser != null)
                {
                    statusLabelUser.Text = "当前用户：未登录";
                }
                
                if (statusLabelConnection != null)
                {
                    statusLabelConnection.Text = "设备状态：无";
                    statusLabelConnection.ForeColor = System.Drawing.Color.Red;
                }
                
                if (statusLabelLockStatus != null)
                {
                    statusLabelLockStatus.Text = "加锁状态：无";
                    statusLabelLockStatus.ForeColor = System.Drawing.Color.Gray;
                }
                
                if (statusLabelTime != null)
                {
                    statusLabelTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化状态栏时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化
        /// </summary>
        private void Init()
        {
            this.bleCore.MessAgeLog += BleCore_MessAgeLog;
            this.bleCore.DeviceScan += BleCore_DeviceScan;
            this.bleCore.DeviceFindService += BleCore_DeviceFindService;
            this.bleCore.ReceiveNotification += BleCore_ReceiveNotification;
            this.bleCore.DeviceConnectionStatus += BleCore_DeviceConnectionStatus;

            // 初始化状态栏连接状态
            if (statusLabelConnection != null)
            {
                statusLabelConnection.Text = "设备状态：无";
                statusLabelConnection.ForeColor = System.Drawing.Color.Red;
            }
        }

        /// <summary>
        /// 根据用户角色设置界面权限
        /// </summary>
        private void SetupRoleBasedUI()
        {
            Console.WriteLine("SetupRoleBasedUI 开始执行");
            
            // 设置窗体标题显示当前用户信息
            this.Text = $"YSJ-10 校准上位机 - {PermissionManager.GetCurrentUserName()}";
            Console.WriteLine($"窗体标题设置为: {this.Text}");
            
            // 更新状态栏用户信息显示
            if (statusLabelUser != null)
            {
                statusLabelUser.Text = $"当前用户：{PermissionManager.GetCurrentUserName()}";
                Console.WriteLine($"状态栏用户标签设置为: {statusLabelUser.Text}");
            }
            else
            {
                Console.WriteLine("statusLabelUser 为 null");
            }
            
            // 同时更新兼容性标签（如果存在）
            if (lblCurrentUser != null)
            {
                lblCurrentUser.Text = $"当前用户：{PermissionManager.GetCurrentUserName()}";
                Console.WriteLine($"兼容性用户标签设置为: {lblCurrentUser.Text}");
            }
            
            // 根据用户角色控制功能权限
            var currentRole = PermissionManager.GetCurrentUserRole();
            Console.WriteLine($"当前用户角色: {currentRole}");
            
            switch (currentRole)
            {
                case UserRole.Admin:
                    Console.WriteLine("执行管理员权限设置");
                    // 管理员可以看到所有功能
                    EnableAllFeatures();
                    break;
                    
                case UserRole.Calibrator:
                    Console.WriteLine("执行校准员权限设置");
                    // 校准员只能看到连接配置和设备校准功能
                    EnableCalibratorFeatures();
                    break;
                    
                case UserRole.Inspector:
                    Console.WriteLine("执行检测员权限设置");
                    // 检测员只能看到连接配置和检验相关功能
                    EnableInspectorFeatures();
                    break;
            }
            
            Console.WriteLine("SetupRoleBasedUI 执行完成");
        }

        /// <summary>
        /// 启用所有功能（管理员）
        /// </summary>
        private void EnableAllFeatures()
        {
            // 管理员可以看到所有功能
            // 显示所有TabPage
            if (tabPageConnection != null) 
            {
                tabPageConnection.Visible = true;
                Console.WriteLine("tabPageConnection 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageConnection 为 null");
            }
            
            if (tabPageCalibration != null) 
            {
                tabPageCalibration.Visible = true;
                Console.WriteLine("tabPageCalibration 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageCalibration 为 null");
            }
            
            if (tabPageTestControl != null) 
            {
                tabPageTestControl.Visible = true;
                Console.WriteLine("tabPageTestControl 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageTestControl 为 null");
            }
            
            if (tabPageTestRecord != null) 
            {
                tabPageTestRecord.Visible = true;
                Console.WriteLine("tabPageTestRecord 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageTestRecord 为 null");
            }
            
            if (tabPageTestResult != null) 
            {
                tabPageTestResult.Visible = true;
                Console.WriteLine("tabPageTestResult 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageTestResult 为 null");
            }
            
            // 启用所有按钮
            EnableAllButtons();
            
            // 强制刷新TabControl显示
            if (tabControlMain != null)
            {
                tabControlMain.Refresh();
                Console.WriteLine("TabControl已刷新");
            }
            
            // 确保所有TabPage都在TabControl中
            if (tabPageCalibration != null && !tabControlMain.TabPages.Contains(tabPageCalibration))
            {
                tabControlMain.TabPages.Add(tabPageCalibration);
                Console.WriteLine("已将校准页面添加到TabControl");
            }
            
            if (tabPageTestControl != null && !tabControlMain.TabPages.Contains(tabPageTestControl))
            {
                tabControlMain.TabPages.Add(tabPageTestControl);
                Console.WriteLine("已将检验控制页面添加到TabControl");
            }
            
            if (tabPageTestRecord != null && !tabControlMain.TabPages.Contains(tabPageTestRecord))
            {
                tabControlMain.TabPages.Add(tabPageTestRecord);
                Console.WriteLine("已将检验记录页面添加到TabControl");
            }
            
            if (tabPageTestResult != null && !tabControlMain.TabPages.Contains(tabPageTestResult))
            {
                tabControlMain.TabPages.Add(tabPageTestResult);
                Console.WriteLine("已将检验结果页面添加到TabControl");
            }
            

        }

        /// <summary>
        /// 启用校准员功能
        /// </summary>
        private void EnableCalibratorFeatures()
        {
            Console.WriteLine("EnableCalibratorFeatures 开始执行");
            
            // 校准员可以看到连接配置和设备校准功能
            // 显示相关TabPage
            if (tabPageConnection != null) 
            {
                tabPageConnection.Visible = true;
                Console.WriteLine("tabPageConnection 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageConnection 为 null");
            }
            
            if (tabPageCalibration != null) 
            {
                tabPageCalibration.Visible = true;
                Console.WriteLine("tabPageCalibration 已设置为可见");
                
                // 确保校准页面在TabControl中
                if (tabControlMain != null && !tabControlMain.TabPages.Contains(tabPageCalibration))
                {
                    tabControlMain.TabPages.Add(tabPageCalibration);
                    Console.WriteLine("已将校准页面添加到TabControl");
                }
            }
            else
            {
                Console.WriteLine("tabPageCalibration 为 null");
            }
            
            // 隐藏检测相关功能
            if (tabPageTestControl != null) 
            {
                tabPageTestControl.Visible = false;
                Console.WriteLine("tabPageTestControl 已设置为隐藏");
                
                // 从TabControl中移除检验控制页面
                if (tabControlMain != null && tabControlMain.TabPages.Contains(tabPageTestControl))
                {
                    tabControlMain.TabPages.Remove(tabPageTestControl);
                    Console.WriteLine("已从TabControl中移除检验控制页面");
                }
            }
            else
            {
                Console.WriteLine("tabPageTestControl 为 null");
            }
            
            if (tabPageTestRecord != null) 
            {
                tabPageTestRecord.Visible = false;
                Console.WriteLine("tabPageTestRecord 已设置为隐藏");
                
                // 从TabControl中移除检验记录页面
                if (tabControlMain != null && tabControlMain.TabPages.Contains(tabPageTestRecord))
                {
                    tabControlMain.TabPages.Remove(tabPageTestRecord);
                    Console.WriteLine("已从TabControl中移除检验记录页面");
                }
            }
            else
            {
                Console.WriteLine("tabPageTestRecord 为 null");
            }
            
            if (tabPageTestResult != null) 
            {
                tabPageTestResult.Visible = false;
                Console.WriteLine("tabPageTestResult 已设置为隐藏");
                
                // 从TabControl中移除检验结果页面
                if (tabControlMain != null && tabControlMain.TabPages.Contains(tabPageTestResult))
                {
                    tabControlMain.TabPages.Remove(tabPageTestResult);
                    Console.WriteLine("已从TabControl中移除检验结果页面");
                }
            }
            else
            {
                Console.WriteLine("tabPageTestResult 为 null");
            }
            
            // 禁用检测相关功能
            DisableInspectionFeatures();
            
            // 强制刷新TabControl显示
            if (tabControlMain != null)
            {
                tabControlMain.Refresh();
                Console.WriteLine("TabControl已刷新");
            }
            
            Console.WriteLine("EnableCalibratorFeatures 执行完成");
        }

        /// <summary>
        /// 启用检测员功能
        /// </summary>
        private void EnableInspectorFeatures()
        {
            Console.WriteLine("EnableInspectorFeatures 开始执行");
            
            // 检测员可以看到连接配置和检验相关功能
            // 显示相关TabPage
            if (tabPageConnection != null) 
            {
                tabPageConnection.Visible = true;
                Console.WriteLine("tabPageConnection 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageConnection 为 null");
            }
            
            if (tabPageTestControl != null) 
            {
                tabPageTestControl.Visible = true;
                Console.WriteLine("tabPageTestControl 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageTestControl 为 null");
            }
            
            if (tabPageTestRecord != null) 
            {
                tabPageTestRecord.Visible = true;
                Console.WriteLine("tabPageTestRecord 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageTestRecord 为 null");
            }
            
            if (tabPageTestResult != null) 
            {
                tabPageTestResult.Visible = true;
                Console.WriteLine("tabPageTestResult 已设置为可见");
            }
            else
            {
                Console.WriteLine("tabPageTestResult 为 null");
            }
            
            // 隐藏校准功能
            if (tabPageCalibration != null) 
            {
                tabPageCalibration.Visible = false;
                Console.WriteLine("tabPageCalibration 已设置为隐藏");
                
                // 从TabControl中移除校准页面
                if (tabControlMain != null && tabControlMain.TabPages.Contains(tabPageCalibration))
                {
                    tabControlMain.TabPages.Remove(tabPageCalibration);
                    Console.WriteLine("已从TabControl中移除校准页面");
                }
            }
            else
            {
                Console.WriteLine("tabPageCalibration 为 null");
            }
            
            // 禁用校准相关功能
            Console.WriteLine("开始禁用校准相关功能");
            DisableCalibrationFeatures();
            
            // 强制刷新TabControl显示
            if (tabControlMain != null)
            {
                tabControlMain.Refresh();
                Console.WriteLine("TabControl已刷新");
            }
            
            // 再次确认校准功能被隐藏
            if (tabPageCalibration != null)
            {
                tabPageCalibration.Visible = false;
                Console.WriteLine("再次确认: tabPageCalibration.Visible = false");
            }
            
            // 强制刷新整个窗体
            this.Refresh();
            Console.WriteLine("整个窗体已刷新");
            
            Console.WriteLine("EnableInspectorFeatures 执行完成");
        }

        /// <summary>
        /// 禁用检测相关功能
        /// </summary>
        private void DisableInspectionFeatures()
        {
            // 这里需要根据实际的控件名称来禁用检测相关功能
            // 由于没有看到完整的控件定义，这里提供示例代码
            try
            {
                // 禁用检测相关的按钮和控件
                if (btnExTestRecord != null) btnExTestRecord.Enabled = false;
                // 可以继续添加其他检测相关的控件
            }
            catch (Exception ex)
            {
                // 如果控件不存在，忽略错误
            }
        }

        /// <summary>
        /// 禁用校准相关功能
        /// </summary>
        private void DisableCalibrationFeatures()
        {
            Console.WriteLine("DisableCalibrationFeatures 开始执行");
            
            // 这里需要根据实际的控件名称来禁用校准相关功能
            try
            {
                // 禁用校准相关的按钮和控件
                if (btnAutoCali != null) 
                {
                    btnAutoCali.Enabled = false;
                    Console.WriteLine("btnAutoCali 已禁用");
                }
                else
                {
                    Console.WriteLine("btnAutoCali 为 null");
                }
                
                if (btnEnterCalMode != null) 
                {
                    btnEnterCalMode.Enabled = false;
                    Console.WriteLine("btnEnterCalMode 已禁用");
                }
                else
                {
                    Console.WriteLine("btnEnterCalMode 为 null");
                }
                
                if (btnEnterDarkCal != null) 
                {
                    btnEnterDarkCal.Enabled = false;
                    Console.WriteLine("btnEnterDarkCal 已禁用");
                }
                else
                {
                    Console.WriteLine("btnEnterDarkCal 为 null");
                }
                
                if (btnEnterWhiteCal != null) 
                {
                    btnEnterWhiteCal.Enabled = false;
                    Console.WriteLine("btnEnterWhiteCal 已禁用");
                }
                else
                {
                    Console.WriteLine("btnEnterWhiteCal 为 null");
                }
                
                if (btnSetRatio != null) 
                {
                    btnSetRatio.Enabled = false;
                    Console.WriteLine("btnSetRatio 已禁用");
                }
                else
                {
                    Console.WriteLine("btnSetRatio 为 null");
                }
                
                if (btnSetGPWM != null) 
                {
                    btnSetGPWM.Enabled = false;
                    Console.WriteLine("btnSetGPWM 已禁用");
                }
                else
                {
                    Console.WriteLine("btnSetGPWM 为 null");
                }
                

                
                if (btnExCalRecord != null) 
                {
                    btnExCalRecord.Enabled = false;
                    Console.WriteLine("btnExCalRecord 已禁用");
                }
                else
                {
                    Console.WriteLine("btnExCalRecord 为 null");
                }
                
                Console.WriteLine("所有校准相关按钮已禁用");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"禁用校准功能时发生错误: {ex.Message}");
            }
            
            Console.WriteLine("DisableCalibrationFeatures 执行完成");
        }

        /// <summary>
        /// 启用所有按钮（管理员使用）
        /// </summary>
        private void EnableAllButtons()
        {
            try
            {
                // 启用所有校准相关按钮
                if (btnAutoCali != null) btnAutoCali.Enabled = false;
                if (btnEnterCalMode != null) btnEnterCalMode.Enabled = true;
                if (btnEnterDarkCal != null) btnEnterDarkCal.Enabled = true;
                if (btnEnterWhiteCal != null) btnEnterWhiteCal.Enabled = true;
                if (btnSetRatio != null) btnSetRatio.Enabled = true;
                if (btnSetGPWM != null) btnSetGPWM.Enabled = true;

                if (btnExCalRecord != null) btnExCalRecord.Enabled = true;
                
                // 启用所有检测相关按钮
                if (btnExTestRecord != null) btnExTestRecord.Enabled = true;
                
                // 可以继续添加其他需要启用的按钮
            }
            catch (Exception ex)
            {
                // 如果控件不存在，忽略错误
            }
        }

        /// <summary>
        /// 定时器更新时间事件
        /// </summary>
        private void timerUpdateTime_Tick(object sender, EventArgs e)
        {
            if (statusLabelTime != null)
            {
                statusLabelTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }

        /// <summary>
        /// 退出按钮点击事件
        /// </summary>
        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("确定要退出当前用户吗？", "确认退出", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                // 注销当前用户
                PermissionManager.Logout();
                
                // 关闭当前窗体
                this.Close();
                
                // 重新启动程序，显示登录窗体
                Application.Restart();
            }
        }

        private void GetStardDevVal()
        {
            caliData.calib_Points[0].y_value = (float)Properties.Settings.Default.C0val / 1000;
            caliData.calib_Points[1].y_value = (float)Properties.Settings.Default.C1val / 1000;
            caliData.calib_Points[2].y_value = (float)Properties.Settings.Default.C2val / 1000;
            caliData.calib_Points[3].y_value = (float)Properties.Settings.Default.C3val / 1000;
            caliData.calib_Points[4].y_value = (float)Properties.Settings.Default.C4val / 1000;
            caliData.calib_Points[5].y_value = (float)Properties.Settings.Default.C5val / 1000;
            caliData.calib_Points[6].y_value = (float)Properties.Settings.Default.C6val / 1000;
            caliData.calib_Points[7].y_value = (float)Properties.Settings.Default.C7val / 1000;
            caliData.calib_Points[8].y_value = (float)Properties.Settings.Default.C8val / 1000;
            caliData.calib_Points[9].y_value = (float)Properties.Settings.Default.C9val / 1000;
        }
        //将时间戳转换为具体时间
        public static DateTime ConvertUnixTimestamp(string timestampString)
        {
            // 1. 将字符串转换为 long 类型
            if (!long.TryParse(timestampString, out long seconds))
            {
                throw new ArgumentException("无效的时间戳格式");
            }

            // 2. 创建 Unix 纪元起点 (1970-01-01 00:00:00 UTC)
            DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // 3. 添加秒数（转换为本地时间）
            return unixEpoch.AddSeconds(seconds).ToLocalTime();
        }
        public void ConvertSeconds(uint totalSeconds)
        {
            // 定义时间常量
            const uint SECONDS_PER_MINUTE = 60;
            const uint SECONDS_PER_HOUR = SECONDS_PER_MINUTE * 60;
            const uint SECONDS_PER_DAY = SECONDS_PER_HOUR * 24;

            // 计算各时间单位
            uint days = totalSeconds / SECONDS_PER_DAY;
            uint remainingSeconds = totalSeconds % SECONDS_PER_DAY;

            uint hours = remainingSeconds / SECONDS_PER_HOUR;
            remainingSeconds %= SECONDS_PER_HOUR;

            uint minutes = remainingSeconds / SECONDS_PER_MINUTE;
            uint seconds = remainingSeconds % SECONDS_PER_MINUTE;

            // 格式化输出
            txtRemainTime.Text = days.ToString() + " " +
                                hours.ToString() + ":" + minutes.ToString() + ":" + seconds.ToString();
        }
        
        public class DataPoint
        {
            public long Timestamp { get; set; }
            public int ErrorCode { get; set; }
            public int Value { get; set; }
        }
        private void getE01Msg(byte[] data)
        {
            string mystr = "";
            byte[] mybyteInfo = new byte[data.Length];
            for (UInt32 i = 0; i < data.Length - 2; i++)
            {
                mybyteInfo[i] = data[i + 2];
            }
            string s = "";
            s = System.Text.Encoding.UTF8.GetString(mybyteInfo);
            string[] segments = s.Split(new[] { "=>" }, StringSplitOptions.RemoveEmptyEntries);
            List<DataPoint> dataPoints = new List<DataPoint>();
            foreach (string segment in segments)
            {
                // 按等号分割每个片段
                string[] parts = segment.Split('=');
                if (parts.Length != 3) continue; // 确保格式正确
                                                 //确保时间戳字符串能正常输出
                if (!(parts[1] == "1" || parts[1] == "2" || parts[1] == "3" || parts[1] == "4" || parts[1] == "5"
                    || parts[1] == "6" || parts[1] == "7" || parts[1] == "8" || parts[1] == "200"
                    || parts[1] == "9" || parts[1] == "10" || parts[1] == "11" || parts[1] == "12" || parts[1] == "13"
                    || parts[1] == "14" || parts[1] == "15" || parts[1] == "16" || parts[1] == "17" || parts[1] == "18")
                    || parts[1] == null)
                {
                    mystr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                    break;
                }
                //发送个数
                if (parts[1] == "200")
                {
                    int page = ((Convert.ToInt32(parts[2].ToString())) / 81) + 1;
                    this.lbE01LogPageNum.Text = page.ToString();
                }
                //txtLog.AppendText(parts[1] + "\r\n");
                // 解析数据并创建对象
                if (long.TryParse(parts[0], out long timestamps) &&
                    int.TryParse(parts[1], out int errorCode) &&
                    int.TryParse(parts[2], out int value))
                {
                    dataPoints.Add(new DataPoint
                    {
                        Timestamp = timestamps,
                        ErrorCode = errorCode,
                        Value = value
                    });
                }
            }
            string sArr = "";
            foreach (var dp in dataPoints)
            {
                if (dp.ErrorCode == 4) { sArr = "d值有误"; }
                else if (dp.ErrorCode == 5) { sArr = "g值有误"; }
                else if (dp.ErrorCode == 6) { sArr = "b值有误"; }
                else if (dp.ErrorCode == 7) { sArr = "漏光/温度补偿有误"; }
                else if (dp.ErrorCode == 8) { sArr = "过早抬起探头"; }
                else if (dp.ErrorCode == 9) { sArr = "暗环境下G值过低"; }
                else if (dp.ErrorCode == 10) { sArr = "暗环境下B值过低"; }
                else if (dp.ErrorCode == 11) { sArr = "GPWM过高"; }
                else if (dp.ErrorCode == 12) { sArr = "GPWM过低"; }
                else if (dp.ErrorCode == 13) { sArr = "BPWM过高"; }
                else if (dp.ErrorCode == 14) { sArr = "BPWM过低"; }
                else if (dp.ErrorCode == 15) { sArr = "值正常"; }
                else if (dp.ErrorCode == 16) { sArr = "十次测量中有错误"; }
                else if (dp.ErrorCode == 17) { sArr = "校准平均值超出范围(2600-3300)"; }
                else if (dp.ErrorCode == 18) { sArr = "漏光严重"; }
                else { sArr = "其他错误"; }
                //mystr += "Timestamp:" + formattedDateTime + " " +
                //mystr += "Timestamp:" + dp.Timestamp.ToString() + " " +
                mystr += "Timestamp:" + ConvertUnixTimestamp(dp.Timestamp.ToString()).ToString("yyyy-MM-dd HH:mm:ss") + " " +
                            "Error:" + sArr + " " +
                            "ErrorValue:" + dp.Value.ToString() + "\r\n";
            }

            txtLog.AppendText(mystr + "\r\n");
        }
        private void CalculationOfTest() {
            if ((!string.IsNullOrEmpty(las0_1[0].Text)) && (!string.IsNullOrEmpty(las10_4[0].Text)) && (!string.IsNullOrEmpty(las32[0].Text)) &&
                (!string.IsNullOrEmpty(las0_1[1].Text)) && (!string.IsNullOrEmpty(las10_4[1].Text)) && (!string.IsNullOrEmpty(las32[1].Text)) &&
                (!string.IsNullOrEmpty(las0_1[2].Text)) && (!string.IsNullOrEmpty(las10_4[2].Text)) && (!string.IsNullOrEmpty(las32[2].Text)) &&
                (!string.IsNullOrEmpty(las0_1[3].Text)) && (!string.IsNullOrEmpty(las10_4[3].Text)) && (!string.IsNullOrEmpty(las32[3].Text)) &&
                (!string.IsNullOrEmpty(las0_1[4].Text)) && (!string.IsNullOrEmpty(las10_4[4].Text)) && (!string.IsNullOrEmpty(las32[4].Text)) &&
                (!string.IsNullOrEmpty(las0_1[5].Text)) && (!string.IsNullOrEmpty(las10_4[5].Text)) && (!string.IsNullOrEmpty(las32[5].Text)) &&
                (!string.IsNullOrEmpty(las0_1[6].Text)) && (!string.IsNullOrEmpty(las10_4[6].Text)) && (!string.IsNullOrEmpty(las32[6].Text)) &&
                (!string.IsNullOrEmpty(las0_1[7].Text)) && (!string.IsNullOrEmpty(las10_4[7].Text)) && (!string.IsNullOrEmpty(las32[7].Text)) &&
                (!string.IsNullOrEmpty(las0_1[8].Text)) && (!string.IsNullOrEmpty(las10_4[8].Text)) && (!string.IsNullOrEmpty(las32[8].Text)) &&
                (!string.IsNullOrEmpty(las0_1[9].Text)) && (!string.IsNullOrEmpty(las10_4[9].Text)) && (!string.IsNullOrEmpty(las32[9].Text)))
            {
                //if (lasResult[0].BackColor != Color.Red && lasResult[1].BackColor != Color.Red && lasResult[2].BackColor != Color.Red &&
                //    lasResult[3].BackColor != Color.Red && lasResult[4].BackColor != Color.Red && lasResult[5].BackColor != Color.Red)
                btnExTestRecord.Enabled = true;
                double[] arr32 = new double[10];
                arr32[0] = Convert.ToDouble(las32[0].Text); arr32[1] = Convert.ToDouble(las32[1].Text); arr32[2] = Convert.ToDouble(las32[2].Text);
                arr32[3] = Convert.ToDouble(las32[3].Text); arr32[4] = Convert.ToDouble(las32[4].Text); arr32[5] = Convert.ToDouble(las32[5].Text);
                arr32[6] = Convert.ToDouble(las32[6].Text); arr32[7] = Convert.ToDouble(las32[7].Text); arr32[8] = Convert.ToDouble(las32[8].Text); arr32[9] = Convert.ToDouble(las32[9].Text);
                double mean32 = arr32.Average();
                for (int i = 0; i < 9; i++)
                {
                    for (int k = 0; k < 9 - i; k++)
                    {
                        if (arr32[k] > arr32[k + 1])
                        {
                            double temp = arr32[k];
                            arr32[k] = arr32[k + 1];
                            arr32[k + 1] = temp;
                        }
                    }
                }
                double[] arr0 = new double[10];
                arr0[0] = Convert.ToDouble(las0_1[0].Text); arr0[1] = Convert.ToDouble(las0_1[1].Text); arr0[2] = Convert.ToDouble(las0_1[2].Text);
                arr0[3] = Convert.ToDouble(las0_1[3].Text); arr0[4] = Convert.ToDouble(las0_1[4].Text); arr0[5] = Convert.ToDouble(las0_1[5].Text);
                arr0[6] = Convert.ToDouble(las0_1[6].Text); arr0[7] = Convert.ToDouble(las0_1[7].Text); arr0[8] = Convert.ToDouble(las0_1[8].Text); arr0[9] = Convert.ToDouble(las0_1[9].Text);
                double mean0 = arr0.Average();
                for (int i = 0; i < 9; i++)
                {
                    for (int k = 0; k < 9 - i; k++)
                    {
                        if (arr0[k] > arr0[k + 1])
                        {
                            double temp = arr0[k];
                            arr0[k] = arr0[k + 1];
                            arr0[k + 1] = temp;
                        }
                    }
                }
                double[] arr10 = new double[10];
                arr10[0] = Convert.ToDouble(las10_4[0].Text); arr10[1] = Convert.ToDouble(las10_4[1].Text); arr10[2] = Convert.ToDouble(las10_4[2].Text);
                arr10[3] = Convert.ToDouble(las10_4[3].Text); arr10[4] = Convert.ToDouble(las10_4[4].Text); arr10[5] = Convert.ToDouble(las10_4[5].Text);
                arr10[6] = Convert.ToDouble(las10_4[6].Text); arr10[7] = Convert.ToDouble(las10_4[7].Text); arr10[8] = Convert.ToDouble(las10_4[8].Text); arr10[9] = Convert.ToDouble(las10_4[9].Text);
                double mean10 = arr10.Average();

                double X0 = 0, X10 = 10;
                double E0 = mean0 - X0;
                double E10 = mean10 - X10;

                double sumOfSquares0 = arr0.Select(x => Math.Pow(x - mean0, 2)).Sum();
                double stdDev0 = 0, CV0 = 0;
                if (sumOfSquares0 != 0)
                {
                    stdDev0 = Math.Sqrt(sumOfSquares0 / (arr0.Length - 1));
                    CV0 = (stdDev0 / mean0) * 100;
                }
                double sumOfSquares10 = arr10.Select(x => Math.Pow(x - mean10, 2)).Sum();
                double stdDev10 = Math.Sqrt(sumOfSquares10 / (arr10.Length - 1));
                double CV10 = (stdDev10 / mean10) * 100;

                if (E0 > 1.5 || E0 < -1.5) { lasResult[2].BackColor = Color.Red; txtTestResult.Text = "未通过"; }
                else lasResult[2].BackColor = Color.White;
                if (E10 > 1.5 || E10 < -1.5) { lasResult[3].BackColor = Color.Red; txtTestResult.Text = "未通过"; }
                else lasResult[3].BackColor = Color.White;
                if (CV0 > 10) { lasResult[4].BackColor = Color.Red; txtTestResult.Text = "未通过"; }
                else lasResult[4].BackColor = Color.White;
                if (CV10 > 10) { lasResult[5].BackColor = Color.Red; txtTestResult.Text = "未通过"; }
                else { lasResult[5].BackColor = Color.White; }

                if (txtTestResult.Text != "未通过")
                    txtTestResult.Text = "通过";
                if (lasResult[2].BackColor == Color.White ||
                    lasResult[3].BackColor == Color.White ||
                    lasResult[4].BackColor == Color.White ||
                    lasResult[5].BackColor == Color.White)
                {
                    txtTestResult.Text = "通过";
                }


                lasResult[0].Text = arr0[0].ToString("0.00");//0.0的最小值
                lasResult[1].Text = arr32[9].ToString();//32.0的最大值
                lasResult[2].Text = E0.ToString("0.00");//0.0准确度
                lasResult[3].Text = E10.ToString("0.00");//10.0的准确度
                lasResult[4].Text = CV0.ToString("0.00") + "%";//0.0重复性
                lasResult[5].Text = CV10.ToString("0.00") + "%";//10.0的重复性
                //ReadTestData();
            }
            
        }
        private void GetAndShowTestVal(byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                return;
            }
            //Log.d(TAG, "current state = " + curState);
            // 输出收到的字节码
            UInt32 tmp = 0;
            UInt32 len = 0;
            UInt32 f, j, d, e;
            UInt32[] array_a = new UInt32[12];
            UInt32[] array_b = new UInt32[12];
            UInt32[] array_c = new UInt32[12];
            bool[] arraybool = new bool[12];
            tmp = data[0];
            len = data[1];
            int dataStart = 2;
            tmp = data[4];
            string mystr = " ";
            if (1 == data[dataStart + 1])
            {
                // mystr += "1-OK!";

                dataStart = 3;
                UInt32 a, b, c;
                a = (UInt32)((data[dataStart + 3] << 8) + data[dataStart + 2]);
                b = (UInt32)((data[dataStart + 5] << 8) + data[dataStart + 4]);
                c = (UInt32)((data[dataStart + 7] << 8) + data[dataStart + 6]);
                j = (UInt32)((data[dataStart + 9] << 8) + data[dataStart + 8]);
                if (isSample)//为真是参考样机采样，假时是收集校准通道数据
                {
                    switch (tmp)
                    {
                        case 0:
                            Properties.Settings.Default.C0val = j;
                            break;
                        case 1:
                            Properties.Settings.Default.C1val = j;
                            break;
                        case 2:
                            Properties.Settings.Default.C2val = j;
                            break;
                        case 3:
                            Properties.Settings.Default.C3val = j;
                            break;
                        case 4:
                            Properties.Settings.Default.C4val = j;
                            break;
                        case 5:
                            Properties.Settings.Default.C5val = j;
                            break;
                        case 6:
                            Properties.Settings.Default.C6val = j;
                            break;
                        case 7:
                            Properties.Settings.Default.C7val = j;
                            break;
                        case 8:
                            Properties.Settings.Default.C8val = j;
                            break;
                        case 9:
                            Properties.Settings.Default.C9val = j;
                            break;

                        default:
                            break;
                    }
                    Properties.Settings.Default.Save();
                }
                else
                {
                    if (tmp < 10)
                    {
                        caliData.result[tmp] = j;
                        caliData.datagotten[tmp] = true;
                        if (tmp == 9)
                        {

                        }
                    }

                }
                if ((j % 10) > 5)
                {
                    j = j + 10;
                }

                d = (j / 10) / 100;
                e = (j / 10) % 100;
                f = (UInt32)((data[dataStart + 10] << 8) + data[dataStart + 11]);
                if (e > 9)
                    mystr = d.ToString() + "." + e.ToString();
                else
                    mystr = d.ToString() + ".0" + e.ToString();
                int num = Convert.ToInt32(data[15]) - 1;
                if (d == 32 || d == 31)
                    las32[num].Text = mystr;
                if (d == 10 || d == 9) {
                    las10_4[num].Text = mystr;
                    las10_7[num].Text = mystr;
                }
                if (d == 0){
                    las0_1[num].Text = mystr;
                    las0_3[num].Text = mystr;
                    las0_6[num].Text = mystr;
                }
            }
            else
            {
                mystr += "2-ERR! ";
            }
            CalculationOfTest();
            //string txtwrtstring = DateTime.Now.ToUniversalTime().ToString() + '\t' + mystr + "mg/dL\r\n";
            //WriteTestTxt(txtwrtstring);
        }
        
        private void UpdateTestDataResult(byte[] data) {
            int dataStart = 2;
            if (data[dataStart] != 0xFF)
            {
                byte[] mybyteInfo = new byte[data.Length];
                int err_flag = 0;
                for (UInt32 i = 0; i < data.Length - dataStart; i++)
                {
                    mybyteInfo[i] = data[i + dataStart];
                }
                string mystr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                string[] sArray = mystr.Split(' ');
                for(int i = 0;i < 10; i++)
                {
                    las0_1[i].Text = sArray[i];
                    las0_3[i].Text = sArray[i];
                    las0_6[i].Text = sArray[i];
                }
                for (int i = 10; i < 20; i++)
                {
                    if (Convert.ToDouble(sArray[i]) != 0)
                    {
                        las10_4[i - 10].Text = sArray[i];
                        las10_7[i - 10].Text = sArray[i];
                    }
                    else err_flag = 1;
                }
                for (int i = 20; i < 30; i++)
                {
                    if (Convert.ToDouble(sArray[i]) != 0)
                        las32[i - 20].Text = sArray[i];
                    else err_flag = 1;
                }
                if (err_flag == 1) {
                    ClearTestVal();
                }
                CalculationOfTest();
            }
            else {
                btnExTestRecord.Enabled = false;
            }
        }
        /// <summary>
        /// 异步线程
        /// </summary>
        /// <param name="action"></param>
        public static void RunAsync(Action action)
        {
            ((Action)(delegate ()
            {
                action.Invoke();
            })).BeginInvoke(null, null);
        }
        /// <summary>
        /// 清理所有蓝牙相关数据
        /// </summary>
        private void ClearAllBluetoothData()
        {
            try
            {
                // 先强制断开连接
                if (bleCore != null)
                {
                    try
                    {
                        // 使用同步方法断开连接
                        bleCore.ForceDisconnect();
                        Modify_Auto_Off_Time();
                        EndRecv();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"断开连接失败: {ex.Message}");
                    }
                }

                // 清空所有集合
                deviceServices.Clear();
                writeCharacteristic.Clear();
                notifyCharacteristics.Clear();
                DeviceList.Clear();

                // 重置状态
                btnConn.Text = "连接";
                txtDevName.Text = string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理蓝牙数据时发生异常: {ex.Message}");
                MessageBox.Show($"断开连接时发生异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// 蓝牙状态事件
        /// </summary>
        /// <param name="status"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_DeviceConnectionStatus(BluetoothConnectionStatus status)
        {
            // 更新状态栏连接状态
            if (statusLabelConnection != null)
            {
                if (status == BluetoothConnectionStatus.Connected)
                {
                    statusLabelConnection.Text = "设备状态：已连接";
                    statusLabelConnection.ForeColor = System.Drawing.Color.Green;
                }
                else
                {
                    statusLabelConnection.Text = "设备状态：无";
                    statusLabelConnection.ForeColor = System.Drawing.Color.Red;
                }
            }
            
            if (status == BluetoothConnectionStatus.Disconnected)
            {
                //deviceServices.Clear();
                //writeCharacteristic.Clear();
                //notifyCharacteristics.Clear();
                //Modify_Auto_Off_Time();
                //EndRecv();
                ClearAllBluetoothData();
                
                // 重置加锁状态显示
                if (statusLabelLockStatus != null)
                {
                    statusLabelLockStatus.Text = "加锁状态：无";
                    statusLabelLockStatus.ForeColor = System.Drawing.Color.Gray;
                }
            }
        }

        /// <summary>
        /// 接收通知事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="data"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_ReceiveNotification(GattCharacteristic sender, byte[] data)
        {
            realtimeprocess(data);

        }


        private void realtimeprocess(byte[] data)
        {

            if (data == null || data.Length == 0)
            {
                return;
            }
            //Log.d(TAG, "current state = " + curState);
            // 输出收到的字节码
            int tmp = 0;
            int len = 0;
            tmp = data[0];
            len = data[1];
            UInt32 a, b, c, d, e, f, j, h;
            const int dataStart = 2;
            string mystr = "";
            string myWriteExcelStr = "";
            string writestr = string.Empty;
#if(! GETSKINDATA)
            txtLog.AppendText("Got BLE CMD" + tmp.ToString() + "\r\n");

#endif
            switch (tmp)
            {
                //0x01
                case PCGETmeasureResult:
                    if (0 == data[dataStart])
                    {

                        // mystr += "1-OK!";
                        if (1 == data[dataStart + 1])
                        {
                            a = (UInt32)((data[dataStart + 3] << 8) + data[dataStart + 2]);
                            b = (UInt32)((data[dataStart + 5] << 8) + data[dataStart + 4]);
                            c = (UInt32)((data[dataStart + 7] << 8) + data[dataStart + 6]);
                            j = (UInt32)((data[dataStart + 9] << 8) + data[dataStart + 8]);
                            resultforratio = j;
                            if ((j % 10) > 5)
                            {
                                j = j + 10;
                            }

                            d = (j / 10) / 100;
                            e = (j / 10) % 100;

                            //writestr = DateTime.Now.ToUniversalTime().ToString() + "  D:" + a.ToString() + "   G:" + b.ToString() + "   B:" + c.ToString() + "   R:" + d.ToString() + "." + e.ToString() + " mgDL";
                            //WriteTxt(writestr);
                            if (e > 9)
                            {
#if (!GETSKINDATA)
                                writestr = "  D:" + a.ToString() + "   G:" + b.ToString() + "   B:" + c.ToString() + "   R:" + d.ToString() + "." + e.ToString() + " mgDL";
#else
                                writestr = "    " +a.ToString() + "       " + b.ToString() + "     " + c.ToString() + "     " + d.ToString() + "." + e.ToString() + "  ";
#endif
                            }
                            else
                            {
#if (!GETSKINDATA)
                                writestr = "  D:" + a.ToString() + "   G:" + b.ToString() + "   B:" + c.ToString() + "   R:" + d.ToString() + ".0" + e.ToString() + " mgDL";
#else
                                writestr = "    " + a.ToString() + "      " + b.ToString() + "     " + c.ToString() + "     " + d.ToString() + ".0" + e.ToString() + "";
#endif
                            }
                            f = (UInt32)((data[dataStart + 10] << 8) + data[dataStart + 11]);
                            mystr = writestr + "  " + f.ToString();
                            string txtwrtstring = DateTime.Now.ToUniversalTime().ToString() + mystr + "\r\n";
                            WriteTxt(txtwrtstring);

                        }
                        else
                        {
                            mystr += "2-ERR! ";
                        }
                    }
                    else
                    {
                        mystr += "1-ERROR！";
                    }
                    break;
                case PCGETupdateMeasueWay:
                    break;
                case PCGETansswerMeasureway:
                    break;        //answer set 
                //0x07
                case PCGETdebugInfo:

                    byte[] mybyteInfo = new byte[data.Length];
                    for (UInt32 i = 0; i < data.Length - 2; i++)
                    {
                        mybyteInfo[i] = data[i + 2];
                    }
                    //解析读取历史数据字符串
                    if ((mybyteInfo[0] == '~') && (mybyteInfo[1] == '~') && (mybyteInfo[2] == '~'))
                    {
                        mystr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                        string[] sArray = mystr.Split('\t');
                        //                      stra = stra.Substring(0, stra.firstIndexOf('@'));
                        try
                        {
                            if (sArray.Length == 5)
                            {
                                //string stra = sArray[4].Substring(0, 10);
                                string stra = sArray[4].Substring(0, sArray[4].IndexOf('@'));
                                UInt32 xx = (UInt32)Convert.ToInt32(stra);
                                System.DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new System.DateTime(1970, 1, 1));//当地时区
                                var time = startTime.AddSeconds(xx);
                                mystr = sArray[0] + '\t' + sArray[1] + '\t' + sArray[2] + '\t' + sArray[3] + '\t' + time.ToString();
                                //myWriteExcelStr = txtSN.Text + '\t' + mystr;
                                //WriteExcel(myWriteExcelStr + "\r\n");
                            }
                            else
                            {
                                mystr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                            }
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                    //解析读取RTC时间戳字符串 - 更灵活的识别逻辑
                    else if ((mybyteInfo[0] == 'T') && (mybyteInfo[1] == ':'))
                    {
                        // 添加调试日志
                        string debugStr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                        if (txtLog != null)
                        {
                            txtLog.AppendText("收到数据: " + debugStr + "\r\n");
                        }

                        // 检查是否包含RTC、RESET、ALOCK等关键字
                        bool hasRTC = debugStr.Contains("RTC=");
                        bool hasRESET = debugStr.Contains("RESET=");
                        bool hasALOCK = debugStr.Contains("ALOCK=");

                        if (txtLog != null)
                        {
                            txtLog.AppendText("数据包含 - RTC: " + hasRTC + ", RESET: " + hasRESET + ", ALOCK: " + hasALOCK + "\r\n");
                        }

                        // 如果包含RTC=，则处理时间戳
                        if (hasRTC)
                        {
                        string StampStr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                        string[] StampRTCArr = StampStr.Split(',');
                        string[] StampNumArr = StampRTCArr[0].Split('=');

                            // 添加调试日志
                            if (txtLog != null)
                            {
                                txtLog.AppendText("分割后数组长度: " + StampRTCArr.Length + "\r\n");
                                for (int i = 0; i < StampRTCArr.Length; i++)
                                {
                                    txtLog.AppendText("StampRTCArr[" + i + "]: " + StampRTCArr[i] + "\r\n");
                                }
                            }

                            // 处理RTC时间戳
                            if (StampNumArr.Length >= 2)
                            {
                                try
                                {
                        txtStamp.Text = ConvertUnixTimestamp(StampNumArr[1]).ToString("yyyy-MM-dd HH:mm:ss");
                        int num = 0;
                        int Stamp = (int)timestamp.GetTimeStamp(false);
                                    try
                                    {
                            num = int.Parse(StampNumArr[1]);
                        }
                        catch { }
                        if (num <= Stamp - 3) txtStamp.BackColor = Color.Red;
                        else txtStamp.BackColor = Color.White;
                                }
                                catch (Exception ex)
                                {
                                    if (txtLog != null)
                                    {
                                        txtLog.AppendText("RTC时间戳处理失败: " + ex.Message + "\r\n");
                                    }
                                }
                            }
                        }

                        // 处理RESET=数据，显示在剩余时间文本框中
                        if (hasRESET)
                        {
                            string StampStr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                            string[] StampRTCArr = StampStr.Split(',');

                            for (int i = 0; i < StampRTCArr.Length; i++)
                            {
                                if (StampRTCArr[i].StartsWith("RESET="))
                                {
                                    if (txtLog != null)
                                    {
                                        txtLog.AppendText("找到RESET数据: " + StampRTCArr[i] + "\r\n");
                                    }

                                    string[] resetArr = StampRTCArr[i].Split('=');
                                    if (resetArr.Length == 2)
                                    {
                                        try
                                        {
                                            uint resetSeconds = uint.Parse(resetArr[1]);
                                            ConvertSeconds(resetSeconds);
                                            if (txtLog != null)
                                            {
                                                txtLog.AppendText("RESET处理成功，剩余时间: " + resetSeconds + "秒\r\n");
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            // 如果解析失败，清空剩余时间显示
                                            txtRemainTime.Text = "";
                                            if (txtLog != null)
                                            {
                                                txtLog.AppendText("RESET解析失败: " + ex.Message + "\r\n");
                                            }
                                        }
                                    }
                                    break;
                                }
                            }
                        }

                        // 处理ALOCK=数据，同步到自动上锁按键
                        if (hasALOCK)
                        {
                            string StampStr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                            string[] StampRTCArr = StampStr.Split(',');

                            for (int i = 0; i < StampRTCArr.Length; i++)
                            {
                                if (StampRTCArr[i].StartsWith("ALOCK="))
                                {
                                    if (txtLog != null)
                                    {
                                        txtLog.AppendText("找到ALOCK数据: " + StampRTCArr[i] + "\r\n");
                                    }

                                    string[] alockArr = StampRTCArr[i].Split('=');
                                    if (alockArr.Length == 2)
                                    {
                                        try
                                        {
                                            int alockStatus = int.Parse(alockArr[1]);
                                            if (txtLog != null)
                                            {
                                                txtLog.AppendText("ALOCK状态值: " + alockStatus + "\r\n");
                                            }

                                            if (alockStatus == 0)
                                            {
                                                btnAutoLock.Text = "自动上锁";
                                                if (txtLog != null)
                                                {
                                                    txtLog.AppendText("设置按钮文本为: 自动上锁\r\n");
                                                }
                    }
                    else
                    {
                                                btnAutoLock.Text = "解除自动上锁";
                                                if (txtLog != null)
                                                {
                                                    txtLog.AppendText("设置按钮文本为: 解除自动上锁\r\n");
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            // 如果解析失败，保持默认状态
                                            if (txtLog != null)
                                            {
                                                txtLog.AppendText("ALOCK解析失败: " + ex.Message + "\r\n");
                                            }
                                        }
                    }
                    break;
                                }
                            }
                        }

                    }
                    else if ((mybyteInfo[0] == 'C') && (mybyteInfo[1] == ':')) 
                     {
                         if (txtLog != null)
                         {
                             string debugStr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                             txtLog.AppendText("收到C:zero:air校准数据: " + debugStr + "\r\n");
                         }
                         
                         try
                         {
                             string dataStr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                             
                             // 解析zero:数据 (D0, G0, B0)
                             int zeroStart = dataStr.IndexOf("zero:");
                             int airStart = dataStr.IndexOf("air:");
                             
                             if (zeroStart != -1 && airStart != -1)
                             {
                                 // 提取zero数据部分
                                 string zeroData = dataStr.Substring(zeroStart + 5, airStart - zeroStart - 5);
                                 string[] zeroValues = zeroData.Split(',');
                                 
                                 if (zeroValues.Length >= 3)
                                 {
                                     // 更新D0, G0, B0到表格
                                     if (tableCaliParamView != null && tableCaliParamView.Rows.Count > 0)
                                     {
                                         tableCaliParamView.Rows[0].Cells[1].Value = zeroValues[0].Trim();
                                         tableCaliParamView.Rows[0].Cells[2].Value = zeroValues[1].Trim();
                                         tableCaliParamView.Rows[0].Cells[3].Value = zeroValues[2].Trim();
                                         
                                         // 设置背景色为白色
                                         tableCaliParamView.Rows[0].Cells[1].Style.BackColor = Color.White;
                                         tableCaliParamView.Rows[0].Cells[2].Style.BackColor = Color.White;
                                         tableCaliParamView.Rows[0].Cells[3].Style.BackColor = Color.White;
                                         
                                         if (txtLog != null)
                                         {
                                             txtLog.AppendText("更新zero数据 - D0: " + zeroValues[0].Trim() + 
                                                              ", G0: " + zeroValues[1].Trim() + 
                                                              ", B0: " + zeroValues[2].Trim() + "\r\n");
                                         }
                                         
                                         // 进行校准状态判断，参考UpdateCalDataStateResult函数
                                         try
                                         {
                                             int d0 = int.Parse(zeroValues[0].Trim());
                                             int g0 = int.Parse(zeroValues[1].Trim());
                                             int b0 = int.Parse(zeroValues[2].Trim());
                                             
                                             // 判断D0是否在有效范围内
                                             if (d0 > 50 || d0 < 0)
                                             {
                                                 tableCaliParamView.Rows[0].Cells[1].Style.BackColor = Color.Red;
                                                 if (txtCalResult != null)
                                                 {
                                                     txtCalResult.Text = "未通过";
                                                     txtCalResult.BackColor = Color.Red;
                                                 }
                    }
                    else
                    {
                                                 tableCaliParamView.Rows[0].Cells[1].Style.BackColor = Color.White;
                                             }
                                             
                                             // 判断G0是否在有效范围内
                                             if (g0 > 3300 || g0 < 2600)
                                             {
                                                 tableCaliParamView.Rows[0].Cells[2].Style.BackColor = Color.Red;
                                                 if (txtCalResult != null)
                                                 {
                                                     txtCalResult.Text = "未通过";
                                                     txtCalResult.BackColor = Color.Red;
                                                 }
                                             }
                                             else
                                             {
                                                 tableCaliParamView.Rows[0].Cells[2].Style.BackColor = Color.White;
                                             }
                                             
                                             // 判断B0是否在有效范围内
                                             if (b0 > 3300 || b0 < 2600)
                                             {
                                                 tableCaliParamView.Rows[0].Cells[3].Style.BackColor = Color.Red;
                                                 if (txtCalResult != null)
                                                 {
                                                     txtCalResult.Text = "未通过";
                                                     txtCalResult.BackColor = Color.Red;
                                                 }
                                             }
                                             else
                                             {
                                                 tableCaliParamView.Rows[0].Cells[3].Style.BackColor = Color.White;
                                             }
                                             
                                             // 如果所有值都在范围内，设置为已通过
                                             if (d0 <= 50 && d0 >= 0 && g0 <= 3300 && g0 >= 2600 && b0 <= 3300 && b0 >= 2600)
                                             {
                                                 if (txtCalResult != null)
                                                 {
                                                     txtCalResult.Text = "已通过";
                                                     txtCalResult.BackColor = Color.White;
                                                 }
                                             }
                                         }
                                         catch (Exception ex)
                                         {
                                             if (txtLog != null)
                                             {
                                                 txtLog.AppendText("zero数据数值转换失败: " + ex.Message + "\r\n");
                                             }
                                         }
                                     }
                                 }
                                 
                                 // 提取air数据部分
                                 string airData = dataStr.Substring(airStart + 4);
                                 string[] airValues = airData.Split(',');
                                 
                                 if (airValues.Length >= 3)
                                 {
                                     // 更新D1, G1, B1到表格
                                     if (tableCaliParamView != null && tableCaliParamView.Rows.Count > 0)
                                     {
                                         tableCaliParamView.Rows[0].Cells[4].Value = airValues[0].Trim();
                                         tableCaliParamView.Rows[0].Cells[5].Value = airValues[1].Trim();
                                         tableCaliParamView.Rows[0].Cells[6].Value = airValues[2].Trim();
                                         
                                         // 设置背景色为白色
                                         tableCaliParamView.Rows[0].Cells[4].Style.BackColor = Color.White;
                                         tableCaliParamView.Rows[0].Cells[5].Style.BackColor = Color.White;
                                         tableCaliParamView.Rows[0].Cells[6].Style.BackColor = Color.White;
                                         
                                         if (txtLog != null)
                                         {
                                             txtLog.AppendText("更新air数据 - D1: " + airValues[0].Trim() + 
                                                              ", G1: " + airValues[1].Trim() + 
                                                              ", B1: " + airValues[2].Trim() + "\r\n");
                                         }
                                         
                                         // 进行校准状态判断，参考UpdateCalDataStateResult函数
                                         try
                                         {
                                             int d1 = int.Parse(airValues[0].Trim());
                                             int g1 = int.Parse(airValues[1].Trim());
                                             int b1 = int.Parse(airValues[2].Trim());
                                             
                                             // 判断G1是否在有效范围内
                                             if (g1 > 200 || g1 < 0)
                                             {
                                                 tableCaliParamView.Rows[0].Cells[5].Style.BackColor = Color.Red;
                                                 if (txtCalResult != null)
                                                 {
                                                     txtCalResult.Text = "未通过";
                                                     txtCalResult.BackColor = Color.Red;
                                                 }
                                             }
                                             else
                                             {
                                                 tableCaliParamView.Rows[0].Cells[5].Style.BackColor = Color.White;
                                             }
                                             
                                             // 判断B1是否在有效范围内
                                             if (b1 > 200 || b1 < 0)
                                             {
                                                 tableCaliParamView.Rows[0].Cells[6].Style.BackColor = Color.Red;
                                                 if (txtCalResult != null)
                                                 {
                                                     txtCalResult.Text = "未通过";
                                                     txtCalResult.BackColor = Color.Red;
                                                 }
                                             }
                                             else
                                             {
                                                 tableCaliParamView.Rows[0].Cells[6].Style.BackColor = Color.White;
                                             }
                                             
                                             // 重新检查整体状态，如果之前是已通过，现在某个值超出范围，需要重新设置为未通过
                                             if (txtCalResult != null && txtCalResult.Text == "已通过")
                                             {
                                                 if (g1 > 200 || g1 < 0 || b1 > 200 || b1 < 0)
                                                 {
                                                     txtCalResult.Text = "未通过";
                                                     txtCalResult.BackColor = Color.Red;
                                                 }
                                             }
                                         }
                                         catch (Exception ex)
                                         {
                                             if (txtLog != null)
                                             {
                                                 txtLog.AppendText("air数据数值转换失败: " + ex.Message + "\r\n");
                                             }
                                         }
                                     }
                                 }
                                 
                                 // 设置校准状态
                                 if (txtCalState != null)
                                 {
                                     txtCalState.Text = "已校准";
                                     txtCalState.BackColor = Color.White;
                                 }
                                 
                                 // 根据校准状态和通过状态，控制测试按钮的启用
                                 if (txtCalResult != null && txtCalState != null)
                                 {
                                     if (txtCalResult.Text == "已通过" && txtCalState.Text == "已校准")
                                     {
                                         if (btnTest0mgColorBlock != null) btnTest0mgColorBlock.Enabled = true;
                                         if (btnTest10mgColorBlock != null) btnTest10mgColorBlock.Enabled = true;
                                         if (btnTest32mgColorBlock != null) btnTest32mgColorBlock.Enabled = true;
                                     }
                                     else
                                     {
                                         if (btnTest0mgColorBlock != null) btnTest0mgColorBlock.Enabled = true;
                                         if (btnTest10mgColorBlock != null) btnTest10mgColorBlock.Enabled = true;
                                         if (btnTest32mgColorBlock != null) btnTest32mgColorBlock.Enabled = true;
                                     }
                                 }
                             }
                         }
                         catch (Exception ex)
                         {
                             if (txtLog != null)
                             {
                                 txtLog.AppendText("解析C:zero:air校准数据失败: " + ex.Message + "\r\n");
                             }
                         }
                     }
                     // 处理PWM数据 - 识别第一位为P，第二位为:的格式
                    else if ((mybyteInfo[0] == 'P') && (mybyteInfo[1] == ':'))
                     {
                         if (txtLog != null)
                         {
                             string debugStr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                             txtLog.AppendText("收到PWM数据: " + debugStr + "\r\n");
                         }
                         
                         try
                         {
                             string dataStr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                             
                             // 解析g_pwm数据
                             int gPwmStart = dataStr.IndexOf("g_pwm:");
                             int bPwmStart = dataStr.IndexOf("b_pwm:");
                             
                             if (gPwmStart != -1 && bPwmStart != -1)
                             {
                                 // 提取g_pwm值
                                 string gPwmData = dataStr.Substring(gPwmStart + 6, bPwmStart - gPwmStart - 6);
                                 string gPwmValue = gPwmData.Trim();
                                 
                                 // 清理数据，移除可能的逗号和其他分隔符
                                 gPwmValue = gPwmValue.Replace(",", "").Replace(";", "").Replace(":", "");
                                 
                                 if (txtGPWM != null)
                                 {
                                     txtGPWM.Text = gPwmValue;
                                     if (txtLog != null)
                                     {
                                         txtLog.AppendText("更新G_PWM: " + gPwmValue + "\r\n");
                                     }
                                 }
                                 
                                 // 提取b_pwm值
                                 string bPwmData = dataStr.Substring(bPwmStart + 6);
                                 string bPwmValue = bPwmData.Trim();
                                 
                                 // 清理数据，移除可能的逗号和其他分隔符
                                 bPwmValue = bPwmValue.Replace(",", "").Replace(";", "").Replace(":", "");
                                 
                                 if (txtBPWM != null)
                                 {
                                     txtBPWM.Text = bPwmValue;
                                     if (txtLog != null)
                                     {
                                         txtLog.AppendText("更新B_PWM: " + bPwmValue + "\r\n");
                                     }
                                 }
                             }
                         }
                         catch (Exception ex)
                         {
                             if (txtLog != null)
                             {
                                 txtLog.AppendText("解析PWM数据失败: " + ex.Message + "\r\n");
                             }
                         }
                     }
                    else
                    {
                        mystr = System.Text.Encoding.UTF8.GetString(mybyteInfo);
                    }
                    break;
                //0x09
                case PCGETlockStatus:
                    if (1 == data[dataStart])
                    {
                        btnLock.Text = "加锁";
                    }
                    else
                    {
                        btnLock.Text = "解锁";
                    }
                    // 根据文本设置颜色和状态栏
                    UpdateLockButtonColor();
                    UpdateStatusBarLockStatus();
                    break;
                //0x0d
                case PCGETreadmac:
                    byte[] mybytemacarray = new byte[6];
                    string xxstring = string.Empty;
                    for (uint i = 0; i < 6; i++)
                    {
                        mybytemacarray[i] = data[dataStart + i];
                        // xxstring += Convert.ToInt32(mybytemacarray[i]).ToString;
                        xxstring += mybytemacarray[i].ToString("X2") + ":";
                    }

                    lbMacAddr.Text = xxstring.Substring(0, xxstring.Length - 1);
                    break;
                //0x0f
                case PCGETreadSn:
                    byte[] mybytesnarray = new byte[20];
                    for (uint i = 0; i < 20; i++)
                    {
                        mybytesnarray[i] = data[dataStart + i];
                    }
                    if (mybytesnarray[0] != 0xFF){
                        txtSN.Text = System.Text.Encoding.UTF8.GetString(mybytesnarray);
                        txtSN.BackColor = Color.White;
                        btnEnterCalMode.Enabled = true;
                        btnEnterWhiteCal.Enabled = false;
                        btnEnterDarkCal.Enabled = false;
                        btnTest0mgColorBlock.Enabled = true;    //f
                        btnTest10mgColorBlock.Enabled = true;
                        btnTest32mgColorBlock.Enabled = true;
                    }
                    else{
                        txtSN.BackColor = Color.Red;
                        btnEnterCalMode.Enabled = false;
                        btnEnterWhiteCal.Enabled = false;
                        btnEnterDarkCal.Enabled = false;
                        btnTest0mgColorBlock.Enabled = true;
                        btnTest10mgColorBlock.Enabled = true;
                        btnTest32mgColorBlock.Enabled = true;   //f
                    }
                    mystr = Convert.ToString(mybytesnarray);
                    txtSN.Text = System.Text.Encoding.UTF8.GetString(mybytesnarray);
                    break;
                case PCGETsetSn:
                    break;
                //0x13
                case PCGETcalResult:
                    a = (UInt32)((data[6] << 24) + (data[5] << 16) + (data[4] << 8) + data[3]);
                    b = (UInt32)((data[10] << 24) + (data[9] << 16) + (data[8] << 8) + data[7]);
                    c = (UInt32)((data[14] << 24) + (data[13] << 16) + (data[12] << 8) + data[11]);
                    d = (UInt32)((data[18] << 24) + (data[17] << 16) + (data[16] << 8) + data[15]);
                    e = (UInt32)((data[22] << 24) + (data[21] << 16) + (data[20] << 8) + data[19]);
                    f = (UInt32)((data[26] << 24) + (data[25] << 16) + (data[24] << 8) + data[23]);
                    h = (UInt32)((data[29] << 8) + data[30]);
                    mystr = " 校准值:" + a.ToString() + "，" + b.ToString() + "，" + c.ToString() + "," + d.ToString() + "," + e.ToString() + "," + f.ToString();
                    mystr += "   GPWM=" + data[27].ToString() + "    BPWM=" + data[28].ToString() + "tempture=" + h.ToString();
                    //   writestr = "  D:" + a.ToString() + "   G:" + b.ToString() + "   B:" + c.ToString() + "   R:" + d.ToString() + ".0" + e.ToString() + " mgDL";//  mystr = writestr;
                    break;
                //0x14
                case PCGETcalInfo:
                    a = (UInt32)((data[2] << 8) + data[3]);
                    b = (UInt32)((data[4] << 8) + data[5]);
                    mystr = " 温度校准值:" + a.ToString() + "，" + b.ToString();
                    break;
                case PCGETdecvName:
                    break;
                //0x1B
                case PCGETcalRadio:
                    mystr = "比率 =" + data[2].ToString();

                    lbADJRatio.Text = data[2].ToString();
                    txtShowSetRatio.Text = data[2].ToString();
                    break; //answer setCalRadio  
                //0x1D
                case PCGETanswercalRadio:
                    mystr = "设置比率成功 =" + data[2].ToString();
                    break; //answer setCalRadio    
                //0x21
                case PCGETdecVer:
                    byte[] mybytesnarrayx = new byte[60];
                    for (uint i = 0; i < data.Length - 2; i++)
                    {
                        mybytesnarrayx[i] = data[dataStart + i];
                    }
                    lbFirmwareVer.Text = System.Text.Encoding.UTF8.GetString(mybytesnarrayx);
                    break;
                //0x31
                case PCGETCaliVal:
                    RecvCalVal(data);
                    break;

                //0x45
                case PCGETE01Msg:
                    getE01Msg(data);
                    break;
                //0x4D
                case PCSETAutolockStatus: {
                        byte[] remainStamp = new byte[data.Length];
                        for (UInt32 i = 0; i < data.Length - 3; i++)
                        {
                            remainStamp[i] = data[i + 3];
                        }
                        if (1 == data[dataStart])//自动上锁
                        {
                            btnAutoLock.Text = "自动上锁";
                            if (remainStamp[0] == 0 && remainStamp[1] == 0 &&
                            remainStamp[2] == 0 && remainStamp[3] == 0)
                            {
                                txtRemainTime.Text = "已自动上锁";
                                txtRemainTime.BackColor = Color.Red;
                            }
                        }
                        else//解除自动上锁
                        {
                            btnAutoLock.Text = "解除自动上锁";
                            if (remainStamp[0] == 0 && remainStamp[1] == 0 &&
                            remainStamp[2] == 0 && remainStamp[3] == 0)
                            {
                                txtRemainTime.Text = "";
                                txtRemainTime.BackColor = Color.White;
                            }
                        }

                        if (remainStamp[0]!=0 || remainStamp[1] != 0 || remainStamp[2] != 0 || remainStamp[3] != 0) {
                            uint rStamp = (uint)(
                                (remainStamp[0] << 24) |
                                (remainStamp[1] << 16) |
                                (remainStamp[2] << 8)  |
                                 remainStamp[3]
                            );
                            ConvertSeconds(rStamp);
                        }
                    }
                    break;
                //0x4F
                case PCGETTestVal: {
                        GetAndShowTestVal(data);
                    }
                    break;
                //0x51
                case PCGETAutoTestVal:{
                        UpdateTestDataResult(data);
                    }
                    break;
                default:
                    break;
            }
            txtLog.AppendText(mystr + "\r\n");

        }


        /// <summary>
        /// 获取服务事件
        /// </summary>
        /// <param name="deviceService"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_DeviceFindService(DeviceService deviceService)
        {
            deviceServices.Add(deviceService);
            //           int x = 0xFF;
        }

        /// <summary>
        /// 搜索蓝牙事件
        /// </summary>
        /// <param name="bluetoothLEDevice"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_DeviceScan(BluetoothLEDevice bluetoothLEDevice)
        {
            RunAsync(() =>
            {
                byte[] _Bytes1 = BitConverter.GetBytes(bluetoothLEDevice.BluetoothAddress);
                Array.Reverse(_Bytes1);
                this.DeviceList.Add(bluetoothLEDevice);
            });
        }

        /// <summary>
        /// 提示信息事件
        /// </summary>
        /// <param name="type"></param>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void BleCore_MessAgeLog(MsgType type, string message, byte[] data = null)
        {
            RunAsync(() =>
            {
                if (message != string.Empty)
                {

                    if (message == "Connected")
                    {
                        btnConn.Text = "断开";
                        Thread.Sleep(2000);

                        StartRecv();
                        Thread.Sleep(200);
                        ReadMac();
                        Thread.Sleep(200);
                        ReadStamp();
                        Thread.Sleep(200);
                        ReadDevSn();
                        Thread.Sleep(1000);
                        ReadDevLockStatus();
                        Thread.Sleep(200);
                        Getadjrtio();
                        Thread.Sleep(200);
                        //ReadDevVer();
                        //Thread.Sleep(200);
                    }

                    if (message.Contains("YSJ-105"))
                    {
                        this.txtDevName.Text = message.Substring(5, 13);
                        this.txtLog.AppendText("设备名" + this.txtDevName.Text + "\r\n");
                    }
                    this.txtLog.AppendText(DateTime.Now.ToString("HH:mm:ss.fff", DateTimeFormatInfo.InvariantInfo) + ": " + message + "\r\n");
                }
            });
        }
        


        /// <summary>
        /// 扫描时间
        /// </summary>
        public int bleScanCount = 0;
        /// <summary>
        /// 按钮触发扫描事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnScan_Click(object sender, EventArgs e)
        {
            if (this.btnScan.Text == "扫描蓝牙")
            {
                this.txtLog.Clear();
                this.bleCore.StartBleDeviceWatcher();
                this.btnScan.Text = "停止扫描";
                this.bleScanCount = 15;
                timerBleScan.Enabled = true;
            }
            else
            {
                timerBleScan.Enabled = false;
                this.bleCore.StopBleDeviceWatcher();
                this.btnScan.Text = "扫描蓝牙";
            }
        }

        /// <summary>
        /// 扫描时间
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void timerBleScan_Tick(object sender, EventArgs e)
        {
            this.btnScan.Text = "停止扫描(" + bleScanCount + ")";
            if (bleScanCount-- == 0)
            {
                this.bleCore.StopBleDeviceWatcher();
                this.btnScan.Text = "扫描蓝牙";
                timerBleScan.Enabled = false;
            }
        }

        /// <summary>
        /// 连接蓝牙事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void btnConn_Click(object sender, EventArgs e)
        {
            string mac = txtDevName.Text.Trim();
            BluetoothLEDevice device = DeviceList.Where(c => c.Name == mac)?.FirstOrDefault();
            if (bleCore.BlueConnectStatus == false)
            {

                if (string.IsNullOrEmpty(mac))
                {
                    MessageBox.Show("请输入蓝牙MAC地址");
                    return;
                }

                if (device != null)
                {
                    bleCore.CurrentDevice?.Dispose();
                    deviceServices.Clear();
                    notifyCharacteristics.Clear();
                    writeCharacteristic.Clear();
                    bleCore.StartMatching(device);
                }
                else
                {
                    MessageBox.Show("没有发现此蓝牙，请重新搜索.");
                    //this.btnServer.Enabled = false;
                }
            }
            else
            {
                await Task.Run(() => ClearAllBluetoothData());
            }
            

        }



        //连接上位机后自动执行操作
        private async void StartRecv()
        {
            txtLog.AppendText($"deviceServices count: {deviceServices.Count}\r\n");
            foreach (var device in deviceServices)
            {
                if (device.gattCharacteristic == null || device.gattCharacteristic.Count < 1)
                {
                    continue;
                }
                if (device.gattDeviceService.Uuid != RX_SERVICE_UUID)
                {
                    continue;
                }
                foreach (var notifyCharacteristic in device.gattCharacteristic)
                {
                    /*await */
                    bleCore.SetNotify(notifyCharacteristic,true);
                }
            }
        }
        public void EndRecv()
        {
            txtLog.AppendText($"deviceServices count: {deviceServices.Count}\r\n");
            foreach (var device in deviceServices)
            {
                if (device.gattCharacteristic == null || device.gattCharacteristic.Count < 1)
                {
                    continue;
                }
                if (device.gattDeviceService.Uuid != RX_SERVICE_UUID)
                {
                    continue;
                }
                foreach (var notifyCharacteristic in device.gattCharacteristic)
                {
                    /*await */
                    bleCore.SetNotify(notifyCharacteristic,false);
                }
            }
        }
        private void ReadMac()//0x0C
        {
            byte[] start2a = new byte[] { (byte)0x0C, (byte)0x00 };
            WriteData(start2a);
        }
        private void ReadStamp()//0x3C
        {
            byte[] start2a = new byte[] { (byte)0x3C, (byte)0x00 };
            WriteData(start2a);
        }
        private void ClearTestVal()
        {
            for (int i = 0; i < 10; i++)
            {
                las0_1[i].Text = string.Empty;
                las0_3[i].Text = string.Empty;
                las0_6[i].Text = string.Empty;
                las10_4[i].Text = string.Empty;
                las10_7[i].Text = string.Empty;
                las32[i].Text = string.Empty;
            }
            for (int i = 0; i < 6; i++)
                lasResult[i].Text = string.Empty;
            txtTestResult.Text = string.Empty;
            btnExTestRecord.Enabled = false;
        }
        public void DelCalData()
        {
            tableCaliParamView.Rows[0].Cells[1].Value = "";
            tableCaliParamView.Rows[0].Cells[2].Value = "";
            tableCaliParamView.Rows[0].Cells[3].Value = "";
            tableCaliParamView.Rows[0].Cells[4].Value = "";
            tableCaliParamView.Rows[0].Cells[5].Value = "";
            tableCaliParamView.Rows[0].Cells[6].Value = "";
            txtBPWM.Text = "";
            txtGPWM.Text = "";
            txtCalResult.Text = "";
            txtCalState.Text = "待校准";
            btnExCalRecord.Enabled = false;
        }


        private void ReadDevSn()//0x0E
        {
            byte[] start2a = new byte[] { (byte)0x0E, (byte)0x00 };
            WriteData(start2a);
        }
        

        private void ReadDevLockStatus()//0x08
        {
            byte[] start2a = new byte[] { (byte)0x08, (byte)00 };
            WriteData(start2a);
        }
        private void ReadAutoDevLockStatus()//0x4C
        {
            byte[] start2a = new byte[] { (byte)0x4C, (byte)0x00 };
            WriteData(start2a);
        }
        private void ReadDevVer()//0x20
        {
            byte[] start2a = new byte[] { (byte)0x20, (byte)0x00 };
            WriteData(start2a);
        }
        private void Getadjrtio()//0x1A
        {
            byte[] start2a = new byte[2];
            start2a[0] = 0x1A;
            start2a[1] = 0x00;
            WriteData(start2a);
        }
        void Get_device_unit()//0x42
        {
            byte[] start2a = new byte[2];
            start2a[0] = 0x42;
            start2a[1] = 0x00;
            WriteData(start2a);
        }
        private void ModifytxtGPWM()
        {
            txtGPWMSetShow.Text = "180";
        }



        private void DevLockOrUnlock(bool bstamp)//0x0A
        {
            byte[] start2a = new byte[] { (byte)0x0a, (byte)0x06, (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x03 };
            //{ (byte)0x0A, (byte)0x01, (byte)0x01 };
            UInt32 tmp = (UInt32)timestamp.GetTimeStamp(false) + timetest;
            start2a[0] = 0x0A;
            start2a[1] = 5;
            if (bstamp == true)
            {//加锁
                start2a[2] = 01;
            }
            else if (bstamp == false)
            { //解锁
                start2a[2] = 0;
            }
            start2a[3] = (byte)0xff;
            start2a[4] = (byte)0xff;
            start2a[5] = (byte)0xff;
            start2a[6] = (byte)0xfE;

            WriteData(start2a);
        }
        
        /// <summary>
        /// 根据按钮文本动态设置颜色
        /// </summary>
        private void UpdateLockButtonColor()
        {
            try
            {
                if (btnLock != null)
                {
                    if (btnLock.Text == "解锁")
                    {
                        btnLock.BackColor = Color.Red;  // 加锁状态显示绿色
                    }
                    else if (btnLock.Text == "加锁")
                    {
                        btnLock.BackColor = Color.Green;    // 解锁状态显示红色
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新锁按钮颜色时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新状态栏加锁状态
        /// </summary>
        private void UpdateStatusBarLockStatus()
        {
            try
            {
                if (statusLabelLockStatus != null && btnLock != null)
                {
                    // 只有在真正确定设备状态时才显示具体状态
                    // 默认情况下显示"无"
                    if (btnLock.Text == "解锁")
                    {
                        statusLabelLockStatus.Text = "加锁状态：已加锁";
                        statusLabelLockStatus.ForeColor = System.Drawing.Color.Red;
                    }
                    else if (btnLock.Text == "加锁")
                    {
                        statusLabelLockStatus.Text = "加锁状态：已解锁";
                        statusLabelLockStatus.ForeColor = System.Drawing.Color.Green;
                    }
                    else
                    {
                        // 默认状态：未确定或未连接
                        statusLabelLockStatus.Text = "加锁状态：无";
                        statusLabelLockStatus.ForeColor = System.Drawing.Color.Gray;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新状态栏加锁状态时出错: {ex.Message}");
            }
        }
        
        private void btnLock_Click(object sender, EventArgs e)
        {//根据按钮文本动态设置颜色
            if (btnLock.Text == "加锁")
            {
                DevLockOrUnlock(false);
                btnLock.Text = "解锁";
                // 同步更新状态栏加锁状态
                UpdateStatusBarLockStatus();
            }
            else
            {
                DevLockOrUnlock(true);
                btnLock.Text = "加锁";
                // 同步更新状态栏加锁状态
                UpdateStatusBarLockStatus();
            }
            // 根据文本设置颜色
            UpdateLockButtonColor();
        }
        private void DevAutoLockOrUnlock(bool bstamp)//0x4B
        {
            try
        {
            byte[] start2a = new byte[] { (byte)0x4B, (byte)0x05, (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x03 };
                
                // 从文本框读取天数，默认为1天
                int days = 1;
                if (txtAutoLockDays != null && int.TryParse(txtAutoLockDays.Text, out int inputDays) && inputDays > 0 && inputDays <= 365)
                {
                    days = inputDays;
                }
                else
                {
                    // 输入无效，使用默认值并提示用户
                    if (txtAutoLockDays != null)
                    {
                        txtAutoLockDays.Text = "1";
                    }
                    if (txtLog != null)
                    {
                        txtLog.AppendText("警告：输入天数无效，使用默认值1天\r\n");
                    }
                    Console.WriteLine("警告：txtAutoLockDays 控件未找到或输入无效，使用默认值1天");
                }
                
                // 计算东八区时间戳：当前时间 + 指定天数
                DateTime now = DateTime.Now;
                DateTime targetTime = now.AddDays(days);
                
                // 转换为Unix时间戳（秒）
                DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                UInt32 tmp = (UInt32)(targetTime.ToUniversalTime() - unixEpoch).TotalSeconds;
                
            start2a[0] = 0x4B;
            start2a[1] = 5;
            if (bstamp == true)
            {//自动上锁
                start2a[2] = 1;
            }
            else if (bstamp == false)
            {//自动解锁
                start2a[2] = 0;
            }
            start2a[3] = (byte)((tmp >> 24) & 0xff);
            start2a[4] = (byte)((tmp >> 16) & 0xff);
            start2a[5] = (byte)((tmp >> 8) & 0xff);
            start2a[6] = (byte)((tmp) & 0xff);
                
                // 记录日志
                if (txtLog != null)
                {
                    txtLog.AppendText($"设置自动上锁: {days}天后 ({targetTime:yyyy-MM-dd HH:mm:ss})\r\n");
                    txtLog.AppendText($"时间戳: {tmp}\r\n");
                }
                else
                {
                    Console.WriteLine($"设置自动上锁: {days}天后 ({targetTime:yyyy-MM-dd HH:mm:ss})");
                    Console.WriteLine($"时间戳: {tmp}");
                }
                
            WriteData(start2a);
        }
            catch (Exception ex)
            {
                Console.WriteLine($"DevAutoLockOrUnlock 出错: {ex.Message}");
                if (txtLog != null)
                {
                    txtLog.AppendText($"错误：自动上锁/解锁操作失败 - {ex.Message}\r\n");
                }
            }
        }
        private void btnReadRemainTime_Click(object sender, EventArgs e)
        {
            try
            {
                // 调用ReadStamp函数发送0x3C指令
                ReadStamp();
                // 在日志中记录操作
                if (txtLog != null)
                {
                    txtLog.AppendText("读取剩余时间操作已执行\r\n");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取剩余时间按钮点击出错: {ex.Message}");
                if (txtLog != null)
                {
                    txtLog.AppendText($"错误：读取剩余时间失败 - {ex.Message}\r\n");
                }
            }
        }

        private void btnMeasureOnce_Click(object sender, EventArgs e)
        {
            try
            {
                // 调用CalGetVal函数，传参为0x01
                CalGetVal(0x01);
                // 在日志中记录操作
                if (txtLog != null)
                {
                    txtLog.AppendText("测量一次操作已执行\r\n");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测量一次按钮点击出错: {ex.Message}");
                if (txtLog != null)
                {
                    txtLog.AppendText($"错误：测量一次失败 - {ex.Message}\r\n");
                }
            }
        }

        private void btnAutoLock_Click(object sender, EventArgs e)
        {
            if (btnAutoLock.Text == "自动上锁")
            {
                DevAutoLockOrUnlock(true);
                btnAutoLock.Text = "解除自动上锁";
                txtRemainTime.Text = "";
                txtRemainTime.BackColor = Color.White;
            }
            else
            {
                DevAutoLockOrUnlock(false);
                btnAutoLock.Text = "自动上锁";
                txtRemainTime.Text = "";
            }
        }






        //发送
        private void WriteDevSn()//0x10
        {
            byte[] snpreBytes = System.Text.Encoding.UTF8.GetBytes(txtSN.Text);
            byte[] snBytes = new byte[22];
            snBytes[0] = 0x10;
            snBytes[1] = 0x14;
            for (uint i = 0; i < snpreBytes.Length; i++)
            {
                snBytes[2 + i] = snpreBytes[i];
            }
            if (txtSN.Text == "") snBytes[2] = 0xFF;
            txtSN.Text = "";
            WriteData(snBytes);
            ReadDevSn();
            if (txtSN.Text == string.Empty)
            {
                txtSN.BackColor = Color.Red;
                btnEnterCalMode.Enabled = false;
                btnExCalRecord.Enabled = false;
            }
            else txtSN.BackColor = Color.White;
        }
        private void btnWSN_Click(object sender, EventArgs e)  //写SN
        {
            WriteDevSn();
        }
        private void askCali(byte val)//0x16
        {
            byte[] mybyte = new byte[] { (byte)0x16, (byte)0x01, (byte)val };
            WriteData(mybyte);
        }
        //进入校准模式
        private void btnEnterCalMode_Click(object sender, EventArgs e)//0x16
        {
            Thread.Sleep(300);
            // UInt32 ttimestamp = 0;
            askCali((byte)0x04);
            btnEnterWhiteCal.Enabled = true;
            btnAutoCali.Enabled = true;
        }
        //进入白板校准模式
        private void btnEnterWhiteCal_Click(object sender, EventArgs e)//0x16
        {
            askCali((byte)0x05);
            btnEnterDarkCal.Enabled = true;
            btnEnterWhiteCal.Enabled = false;
        }
        //进入暗环境校准模式
        private void btnEnterDarkCal_Click(object sender, EventArgs e)//0x16
        {
            askCali((byte)0x06);
            btnEnterDarkCal.Enabled = false;
            btnEnterCalMode.Text = "进入校准模式";
        }
        private void btnAutoCali_Click(object sender, EventArgs e)//0x16
        {
            askCali((byte)0x07);
            btnEnterDarkCal.Enabled = false;
            btnEnterWhiteCal.Enabled = false;
            btnAutoCali.Enabled = false;
        }
        private void btnSetRatio_Click(object sender, EventArgs e)//0x1C
        {
            if (txtShowSetRatio.Text != string.Empty)
            {
                UInt32 calratio = (UInt32)Int32.Parse(txtShowSetRatio.Text);
                byte[] start2a = new byte[6];
                start2a[0] = 0x1C;
                start2a[1] = 0x04;
                start2a[2] = (byte)(calratio & 0xff);
                start2a[3] = 0x00;
                start2a[4] = 0x00;
                start2a[5] = 0x00;
                WriteData(start2a);
                lbADJRatio.Text = txtShowSetRatio.Text;
            }
        }
        private void btnReadHisRecord_Click(object sender, EventArgs e)//0x37
        {
            byte[] start2a = new byte[3];
            start2a[0] = 0x37;
            start2a[1] = 0x01;
            start2a[2] = 00;
            WriteData(start2a);

        }
        private void set_time_stamp()//0x3A
        {
            byte[] start2a = new byte[6];
            UInt32 tmp = (UInt32)timestamp.GetTimeStamp(false);
            start2a[0] = 0x3A;
            start2a[1] = 0x04;
            start2a[2] = (byte)((tmp >> 24) & 0xff);
            start2a[3] = (byte)((tmp >> 16) & 0xff);
            start2a[4] = (byte)((tmp >> 8) & 0xff);
            start2a[5] = (byte)((tmp) & 0xff);

            WriteData(start2a);
            txtStamp.Text = ConvertUnixTimestamp(tmp.ToString()).ToString("yyyy-MM-dd HH:mm:ss");
            txtStamp.BackColor = Color.White;
        }
        private void btnWStamp_Click(object sender, EventArgs e)//写时间戳
        {
            set_time_stamp();
        }

        private void btnReadE01Log_Click(object sender, EventArgs e)//0x44
        {
            if (Convert.ToInt32(txtE01LogNowPage.Text) > 0)
            {
                byte[] start2a = new byte[] { (byte)0x44, (byte)0x01, (byte)Convert.ToInt32(txtE01LogNowPage.Text) };
                WriteData(start2a);
            }
            txtE01LogNowPage.Text = (Convert.ToInt32(txtE01LogNowPage.Text) + 1).ToString();
        }
        private void btnDelE01Log_Click(object sender, EventArgs e)//0x46
        {
            byte[] start2a = new byte[] { (byte)0x46, (byte)0x00 };
            WriteData(start2a);
        }
        private void btnSetGPWM_Click(object sender, EventArgs e)//0x48
        {
            if (Convert.ToInt32(txtGPWMSetShow.Text) <= 100)
            {
                txtGPWMSetShow.BackColor = Color.White;
                byte[] mybyte = new byte[] { (byte)0x48, (byte)0x01, (byte)Convert.ToInt32(this.txtGPWMSetShow.Text) };
                WriteData(mybyte);
            }
            else if (Convert.ToInt32(txtGPWMSetShow.Text) > 100)
            {
                txtGPWMSetShow.BackColor = Color.Red;
                MessageBox.Show("PWM不可超过100");
            }
        }
        private void btnTest0mgColorBlock_Click(object sender, EventArgs e)//0x4E
        {
            //byte[] mybyte = new byte[] { (byte)0x30, (byte)0x01, (byte)0x01 };
            //mybyte[2] = (byte)0;
            //WriteData(mybyte);
            byte[] mybyte = new byte[] { (byte)0x4E, (byte)0x01, (byte)0x01 };
            WriteData(mybyte);
        }
        private void btnTest10mgColorBlock_Click(object sender, EventArgs e)//0x4E
        {
            byte[] mybyte = new byte[] { (byte)0x4E, (byte)0x01, (byte)0x01 };
            WriteData(mybyte);
        }
        private void btnTest32mgColorBlock_Click(object sender, EventArgs e)//0x4E
        {
            byte[] mybyte = new byte[] { (byte)0x4E, (byte)0x01, (byte)0x01 };
            WriteData(mybyte);
        }
        private void btnAutoTest_Click(object sender, EventArgs e)//0x4E
        {
            byte[] mybyte = new byte[] { (byte)0x4E, (byte)0x01, (byte)0x02 };
            WriteData(mybyte);
        }
        private void ReadTestData()//0x50
        {
            byte[] start2a = new byte[] { (byte)0x50, (byte)0x00 };
            WriteData(start2a);
        }
        private void Modify_Auto_Off_Time() //0x52
        {
            byte[] start2a = new byte[] { (byte)0x52, (byte)0x00 };
            WriteData(start2a);
        }
        


        private async void WriteData(byte[] data)
        {
            try
            {
                foreach (var device in deviceServices)
                {
                    if (device.gattDeviceService.Uuid != RX_SERVICE_UUID)
                    {
                        continue;
                    }
                    var serviceUuid = device.gattDeviceService.Uuid;
                    if (device.gattCharacteristic == null || device.gattCharacteristic.Count < 1)
                    {
                        continue;
                    }
                    foreach (var writeCharacteristic in device.gattCharacteristic)
                    {
                        if (writeCharacteristic.Uuid != RX_CHAR_UUID)
                        {
                            continue;
                        }
                        await bleCore.Write(writeCharacteristic, data);
                    }
                }
            }
            catch (Exception e)
            {
                e.ToString();
            }
        }

        



        //接收
        private void RecvCalVal(byte[] data)//0x31
        {
            if (data == null || data.Length == 0)
            {
                return;
            }
            //Log.d(TAG, "current state = " + curState);
            // 输出收到的字节码
            UInt32 tmp = 0;
            UInt32 len = 0;
            UInt32 f, j, d, e;
            UInt32[] array_a = new UInt32[12];
            UInt32[] array_b = new UInt32[12];
            UInt32[] array_c = new UInt32[12];
            bool[] arraybool = new bool[12];
            tmp = data[0];
            len = data[1];
            int dataStart = 2;
            tmp = data[4];
            string mystr = " ";
            if (1 == data[dataStart + 1])
            {
                // mystr += "1-OK!";

                dataStart = 3;
                UInt32 a, b, c;
                a = (UInt32)((data[dataStart + 3] << 8) + data[dataStart + 2]);
                b = (UInt32)((data[dataStart + 5] << 8) + data[dataStart + 4]);
                c = (UInt32)((data[dataStart + 7] << 8) + data[dataStart + 6]);
                j = (UInt32)((data[dataStart + 9] << 8) + data[dataStart + 8]);
                if (isSample)//为真是参考样机采样，假时是收集校准通道数据
                {
                    switch (tmp)
                    {
                        case 0:
                            Properties.Settings.Default.C0val = j;
                            break;
                        case 1:
                            Properties.Settings.Default.C1val = j;
                            break;
                        case 2:
                            Properties.Settings.Default.C2val = j;
                            break;
                        case 3:
                            Properties.Settings.Default.C3val = j;
                            break;
                        case 4:
                            Properties.Settings.Default.C4val = j;
                            break;
                        case 5:
                            Properties.Settings.Default.C5val = j;
                            break;
                        case 6:
                            Properties.Settings.Default.C6val = j;
                            break;
                        case 7:
                            Properties.Settings.Default.C7val = j;
                            break;
                        case 8:
                            Properties.Settings.Default.C8val = j;
                            break;
                        case 9:
                            Properties.Settings.Default.C9val = j;
                            break;

                        default:
                            break;
                    }
                    Properties.Settings.Default.Save();
                }
                else
                {
                    if (tmp < 10)
                    {
                        caliData.result[tmp] = j;
                        caliData.datagotten[tmp] = true;
                        if (tmp == 9)
                        {

                        }
                    }

                }
                if ((j % 10) > 5)
                {
                    j = j + 10;
                }

                d = (j / 10) / 100;
                e = (j / 10) % 100;
                f = (UInt32)((data[dataStart + 10] << 8) + data[dataStart + 11]);
                mystr = "色板";
                if (e > 9)
                {
                    mystr += "CH:" + (data[4] + 1).ToString() + "   D:" + a.ToString() + "   G:" + b.ToString() + "   B:" + c.ToString() + "   R:" + d.ToString() + "." + e.ToString() + " mgDL";

                }
                else
                {
                    mystr += "CH:" + (data[4] + 1).ToString() + "   D:" + a.ToString() + "   G:" + b.ToString() + "   B:" + c.ToString() + "   R:" + d.ToString() + ".0" + e.ToString() + " mgDL";

                }
                mystr = mystr + "   " + f.ToString();
            }
            else
            {
                mystr += "2-ERR! ";
            }

            txtLog.AppendText(mystr + "\r\n");


        }
        





        //操作
        public bool WriteTxt_NewFile(string fileFullPath, string writeContent, out string errorMsg)
        {
            bool result = false;
            errorMsg = string.Empty;
            try
            {
                using (StreamWriter sw = new StreamWriter(fileFullPath, false))
                {
                    sw.Write(writeContent);

                }
                result = true;
            }
            catch (Exception ex)
            {
                errorMsg = ex.Message + ex.StackTrace;
            }
            return result;
        }
        public bool WriteTxt_AppendFile(string fileFullPath, string writeContent, out string errorMsg)
        {
            bool result = false;
            errorMsg = string.Empty;
            try
            {
                using (StreamWriter sw = new StreamWriter(fileFullPath, true))
                {
                    sw.Write(writeContent);
                }
                result = true;
            }
            catch (Exception ex)
            {
                errorMsg = ex.Message + ex.StackTrace;
            }
            return result;
        }
        public void WriteTxt(string writeContent)
        {
            if (!File.Exists(fileName))
            {
                WriteTxt_NewFile(fileName, writeContent, out errorString);
            }
            else
            {
                WriteTxt_AppendFile(fileName, writeContent, out errorString);
            }
        }
        public void WriteExcel(string excelN,string writeContent)
        {
            try
            {
                // 确保目录存在
                string directory = Path.GetDirectoryName(excelN);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
            if (excelN == CALExcelName)
            {
                if (!File.Exists(excelN))
                {
                        // 创建CSV文件
                        using (StreamWriter sw = new StreamWriter(excelN, false, Encoding.UTF8))
                        {
                            // 写入标题行（使用逗号分隔）
                            sw.WriteLine("序列号,D0,G0,B0,D1,G1,B1,GPWM,BPWM,结果");
                            // 写入数据行（将制表符转换为逗号）
                            string csvLine = writeContent.Replace('\t', ',').Replace("\r\n", "");
                            
                            // 调试：打印CSV转换信息
                            if (txtLog != null)
                            {
                                txtLog.AppendText("CSV转换调试信息:\r\n");
                                txtLog.AppendText("原始数据: " + writeContent + "\r\n");
                                txtLog.AppendText("转换后CSV: " + csvLine + "\r\n");
                                txtLog.AppendText("CSV字段: " + csvLine.Replace(',', '|') + "\r\n");
                            }
                            
                            sw.WriteLine(csvLine);
                    }
                }
                else
                {
                        // 读取现有CSV文件
                        string[] allLines = File.ReadAllLines(excelN, Encoding.UTF8);
                    string targetSN = writeContent.Split('\t')[0];
                    bool found = false;

                    // 创建新内容列表（保留标题行）
                    List<string> newLines = new List<string>();
                    newLines.Add(allLines[0]); // 添加标题行

                    // 处理数据行（从第1行开始，跳过标题行）
                    for (int i = 1; i < allLines.Length; i++)
                    {
                        // 跳过空行
                        if (string.IsNullOrWhiteSpace(allLines[i]))
                            continue;

                            string[] fields = allLines[i].Split(',');

                        // 确保有足够的列且SN匹配
                        if (fields.Length > 0 && fields[0] == targetSN)
                        {
                                // 替换匹配行，将制表符转换为逗号
                                string csvLine = writeContent.Replace('\t', ',').Replace("\r\n", "");
                                newLines.Add(csvLine);
                            found = true;
                        }
                        else
                        {
                            newLines.Add(allLines[i]); // 保留不匹配行
                        }
                    }

                    // 如果未找到匹配行，追加新数据
                    if (!found)
                    {
                            string csvLine = writeContent.Replace('\t', ',').Replace("\r\n", "");
                            newLines.Add(csvLine);
                    }

                    // 重新写入整个文件
                        File.WriteAllLines(excelN, newLines, Encoding.UTF8);
                }
                    txtLog.AppendText("导出成功，请见" + Path.GetFileName(excelN) + "文件\r\n");
            }
            else if (excelN == TestExcelName)
            {
                string newSN = writeContent.Split('\t')[0];
                if (!File.Exists(excelN))
                {
                        // 创建CSV文件
                        using (StreamWriter sw = new StreamWriter(excelN, false, Encoding.UTF8))
                        {
                            // 写入标题行（使用逗号分隔）
                            sw.WriteLine("序列号,序号,测试范围(结果值Min<0.0mg/dL;Max>32.0mg/dL),,精准度±1.5mg/dL,,重复性(<10%),,检验结果");
                            // 写入第二行
                            sw.WriteLine(",,0.0mg/dL,32.0mg/dL,0.0mg/dL,10.0mg/dL,0.0mg/dL,10.0mg/dL,");
                            // 写入数据行（将制表符转换为逗号）
                            string[] lines = writeContent.Split('\r');
                            foreach (string line in lines)
                            {
                                if (!string.IsNullOrWhiteSpace(line))
                                {
                                    string csvLine = line.Replace('\t', ',');
                                    sw.WriteLine(csvLine);
                                }
                            }
                        }
                    }
                    else
                    {
                        // 读取现有CSV文件
                        string[] allLines = File.ReadAllLines(excelN, Encoding.UTF8);
                    string targetSN = writeContent.Split('\t')[0];
                    bool found = false;

                    // 创建新内容列表（保留标题行）
                    List<string> newLines = new List<string>();
                    newLines.Add(allLines[0]); // 添加标题行
                        newLines.Add(allLines[1]); // 添加第二行

                        // 处理数据行（从第2行开始，跳过标题行）
                    for (int i = 2; i < allLines.Length; i++)
                    {
                        // 跳过空行
                        if (string.IsNullOrWhiteSpace(allLines[i]))
                            continue;

                            string[] fields = allLines[i].Split(',');

                        // 确保有足够的列且SN匹配
                        if (fields.Length > 0 && fields[0] == targetSN && (i-2)%11 == 0)
                        {
                                // 替换匹配行，将制表符转换为逗号
                            string[] str = writeContent.Split('\r');
                                for (int j = 0; j < Math.Min(str.Length, 11); j++)
                                {
                                    if (!string.IsNullOrWhiteSpace(str[j]))
                                    {
                                        string csvLine = str[j].Replace('\t', ',');
                                        newLines.Add(csvLine);
                                    }
                                }
                            found = true;
                                i += 10; // 跳过后续的10行数据
                        }
                        else
                        {
                            newLines.Add(allLines[i]); // 保留不匹配行
                        }
                    }

                    // 如果未找到匹配行，追加新数据
                    if (!found)
                    {
                            string[] lines = writeContent.Split('\r');
                            foreach (string line in lines)
                            {
                                if (!string.IsNullOrWhiteSpace(line))
                                {
                                    string csvLine = line.Replace('\t', ',');
                                    newLines.Add(csvLine);
                                }
                            }
                    }

                    // 重新写入整个文件
                        File.WriteAllLines(excelN, newLines, Encoding.UTF8);
                    }
                    txtLog.AppendText("导出成功，请见" + Path.GetFileName(excelN) + "文件\r\n");
                }
            }
            catch (Exception ex)
            {
                if (txtLog != null)
                {
                    txtLog.AppendText("导出CSV文件失败: " + ex.Message + "\r\n");
                }
                MessageBox.Show("导出失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        public void WriteTestTxt(string writeContent)
        {
            if (!File.Exists(TestFileName))
            {
                WriteTxt_NewFile(TestFileName, writeContent, out errorString);
            }
            else
            {
                WriteTxt_AppendFile(TestFileName, writeContent, out errorString);
            }
        }
        private void FormLoad(object sender, EventArgs e)
        {
            string date = DateTime.Now.ToLocalTime().ToString("yyyy-MM-dd");
            fileName = date + ".txt";
            TestFileName = date + "Test.txt";
            
            // 确保doc文件夹存在，并将CSV文件保存在doc文件夹下
            string docPath = Path.Combine(Application.StartupPath, "..", "..", "doc");
            if (!Directory.Exists(docPath))
            {
                try
                {
                    Directory.CreateDirectory(docPath);
                }
                catch (Exception ex)
                {
                    if (txtLog != null)
                    {
                        txtLog.AppendText("创建doc文件夹失败: " + ex.Message + "\r\n");
                    }
                }
            }
            
            excelName = Path.Combine(docPath, date + ".csv");
            TestExcelName = Path.Combine(docPath, date + "Test.csv");
            CALExcelName = Path.Combine(docPath, date + "CAL.csv");
            

            //if (!File.Exists(fileName))
            //{
            //    WriteTxt_NewFile(fileName, "123", out errorString);
            //}
            //else
            //{
            //    WriteTxt_AppendFile(fileName, "456", out errorString);
            //}

            bleCore.BluetoothSignalStress = Convert.ToInt32(txtSignStress.Text);
            txtSignStress.Text = Properties.Settings.Default.signalstress;
            GetStardDevVal();

        }

        private void btnSub5GPWM_Click(object sender, EventArgs e)
        {
            int num = Convert.ToInt32(this.txtGPWMSetShow.Text);
            num -= 5;
            this.txtGPWMSetShow.Text = num.ToString();
        }
        private void btnSub1GPWM_Click(object sender, EventArgs e)
        {
            int num = Convert.ToInt32(this.txtGPWMSetShow.Text);
            num -= 1;
            this.txtGPWMSetShow.Text = num.ToString();
        }
        private void btnAdd1GPWM_Click(object sender, EventArgs e)
        {
            int num = Convert.ToInt32(this.txtGPWMSetShow.Text);
            num += 1;
            if (num > 200)
            {
                //弹窗提示，并将box里的num设置为200
                num = 200;
            }
            this.txtGPWMSetShow.Text = num.ToString();
        }
        private void btnAdd5GPWM_Click(object sender, EventArgs e)
        {
            int num = Convert.ToInt32(this.txtGPWMSetShow.Text);
            num += 5;
            if (num > 200)
            {
                //弹窗提示，并将box里的num设置为200
                num = 200;
            }
            this.txtGPWMSetShow.Text = num.ToString();
        }
        private void btnAdd1Ratio_Click(object sender, EventArgs e)
        {
            int num = Convert.ToInt32(this.txtShowSetRatio.Text);
            num += 1;
            this.txtShowSetRatio.Text = num.ToString();
        }
        private void btnAdd5Ratio_Click(object sender, EventArgs e)
        {
            int num = Convert.ToInt32(this.txtShowSetRatio.Text);
            num += 5;
            this.txtShowSetRatio.Text = num.ToString();
        }
        private void btnSub1Ratio_Click(object sender, EventArgs e)
        {
            int num = Convert.ToInt32(this.txtShowSetRatio.Text);
            num -= 1;
            this.txtShowSetRatio.Text = num.ToString();
        }
        private void btnSub5Ratio_Click(object sender, EventArgs e)
        {
            int num = Convert.ToInt32(this.txtShowSetRatio.Text);
            num -= 5;
            this.txtShowSetRatio.Text = num.ToString();
        }
        private void btnExCalRecord_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtSN.Text) || txtSN.BackColor == Color.Red)
                {
                    MessageBox.Show("请先写入SN号再导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                // 检查校准数据表格是否有数据
                if (tableCaliParamView == null || tableCaliParamView.Rows.Count == 0)
                {
                    MessageBox.Show("没有校准数据可导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                // 构建导出数据字符串
                StringBuilder mystr = new StringBuilder();
                mystr.Append(txtSN.Text).Append('\t');
                
                // 添加校准参数数据
                for (int i = 1; i <= 6; i++)
                {
                    if (tableCaliParamView.Rows[0].Cells[i].Value != null)
                    {
                        mystr.Append(tableCaliParamView.Rows[0].Cells[i].Value.ToString()).Append('\t');
                    }
            else
            {
                        mystr.Append("0\t");
                    }
                }
                
                // 添加PWM数据（清理可能的逗号）
                string gPwmValue = string.IsNullOrEmpty(txtGPWM.Text) ? "0" : txtGPWM.Text.Replace(",", "");
                string bPwmValue = string.IsNullOrEmpty(txtBPWM.Text) ? "0" : txtBPWM.Text.Replace(",", "");
                mystr.Append(gPwmValue).Append('\t');
                mystr.Append(bPwmValue).Append('\t');
                
                // 添加校准结果
                mystr.Append(string.IsNullOrEmpty(txtCalResult.Text) ? "未测试" : txtCalResult.Text).Append("\r\n");
                
                // 调试：打印构建的数据字符串
                string debugStr = mystr.ToString();
                if (txtLog != null)
                {
                    txtLog.AppendText("导出数据调试信息:\r\n");
                    txtLog.AppendText("原始数据: " + debugStr + "\r\n");
                    txtLog.AppendText("数据字段: " + debugStr.Replace('\t', '|') + "\r\n");
                    
                    // 详细分析每个字段
                    string[] fields = debugStr.Split('\t');
                    txtLog.AppendText("字段详细分析:\r\n");
                    txtLog.AppendText("字段0 (序列号): " + (fields.Length > 0 ? fields[0] : "N/A") + "\r\n");
                    txtLog.AppendText("字段1 (D0): " + (fields.Length > 1 ? fields[1] : "N/A") + "\r\n");
                    txtLog.AppendText("字段2 (G0): " + (fields.Length > 2 ? fields[2] : "N/A") + "\r\n");
                    txtLog.AppendText("字段3 (B0): " + (fields.Length > 3 ? fields[3] : "N/A") + "\r\n");
                    txtLog.AppendText("字段4 (D1): " + (fields.Length > 4 ? fields[4] : "N/A") + "\r\n");
                    txtLog.AppendText("字段5 (G1): " + (fields.Length > 5 ? fields[5] : "N/A") + "\r\n");
                    txtLog.AppendText("字段6 (B1): " + (fields.Length > 6 ? fields[6] : "N/A") + "\r\n");
                    txtLog.AppendText("字段7 (GPWM): " + (fields.Length > 7 ? fields[7] : "N/A") + "\r\n");
                    txtLog.AppendText("字段8 (BPWM): " + (fields.Length > 8 ? fields[8] : "N/A") + "\r\n");
                    txtLog.AppendText("字段9 (结果): " + (fields.Length > 9 ? fields[9] : "N/A") + "\r\n");
                    
                    // 显示PWM值的清理信息
                    txtLog.AppendText("PWM值清理信息:\r\n");
                    txtLog.AppendText("原始GPWM: " + txtGPWM.Text + "\r\n");
                    txtLog.AppendText("清理后GPWM: " + gPwmValue + "\r\n");
                    txtLog.AppendText("原始BPWM: " + txtBPWM.Text + "\r\n");
                    txtLog.AppendText("清理后BPWM: " + bPwmValue + "\r\n");
                }
                
                // 导出到CSV
                WriteExcel(CALExcelName, debugStr);
            }
            catch (Exception ex)
            {
                if (txtLog != null)
                {
                    txtLog.AppendText("导出校准记录失败: " + ex.Message + "\r\n");
                }
                MessageBox.Show("导出校准记录失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void btnSub1E01LogPage_Click(object sender, EventArgs e)
        {
            txtE01LogNowPage.Text = (Convert.ToInt32(txtE01LogNowPage.Text) - 1).ToString();
        }
        private void btnAdd1E01LogPage_Click(object sender, EventArgs e)
        {
            txtE01LogNowPage.Text = (Convert.ToInt32(txtE01LogNowPage.Text) + 1).ToString();
        }
        private void btnExTestRecord_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtSN.Text) || txtSN.BackColor == Color.Red)
                {
                    MessageBox.Show("请先写入SN号再导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                // 检查测试数据是否可用
                if (las0_1 == null || las32 == null || las10_4 == null || lasResult == null)
                {
                    MessageBox.Show("测试数据不完整，无法导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                StringBuilder mystr = new StringBuilder();
                mystr.Append(txtSN.Text).Append('\t');
                mystr.Append("1").Append('\t');
                mystr.Append(las0_1[0].Text).Append('\t').Append(las32[0].Text).Append('\t');
                mystr.Append(las0_1[0].Text).Append('\t').Append(las10_4[0].Text).Append('\t');
                mystr.Append(las0_1[0].Text).Append('\t').Append(las10_4[0].Text).Append('\t');
                mystr.Append(string.IsNullOrEmpty(txtTestResult.Text) ? "" : txtTestResult.Text).Append("\r\n");
                
                for (int i = 1; i < 10; i++)
                {
                    mystr.Append("").Append('\t').Append(Convert.ToInt32(i + 1)).Append('\t');
                    mystr.Append(las0_1[i].Text).Append('\t').Append(las32[i].Text).Append('\t');
                    mystr.Append(las0_1[i].Text).Append('\t').Append(las10_4[i].Text).Append('\t');
                    mystr.Append(las0_1[i].Text).Append('\t').Append(las10_4[i].Text).Append('\t');
                    mystr.Append("").Append("\r\n");
                }
                
                mystr.Append("").Append('\t').Append("").Append('\t');
                mystr.Append(lasResult[0].Text).Append('\t').Append(lasResult[1].Text).Append('\t').Append(lasResult[2].Text).Append('\t');
                mystr.Append(lasResult[3].Text).Append('\t').Append(lasResult[4].Text).Append('\t').Append(lasResult[5].Text).Append('\t');
                mystr.Append("").Append("\r\n");
                
                WriteExcel(TestExcelName, mystr.ToString());
            }
            catch (Exception ex)
            {
                if (txtLog != null)
                {
                    txtLog.AppendText("导出测试记录失败: " + ex.Message + "\r\n");
                }
                MessageBox.Show("导出测试记录失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }





        //其他
        private void txtTextChange(object sender, EventArgs e)
        {
            bleCore.BluetoothSignalStress = Convert.ToInt32(txtSignStress.Text);
            Properties.Settings.Default.signalstress = txtSignStress.Text;
        }
        private void btnrdratio_Click(object sender, EventArgs e)
        {
            Getadjrtio();
        }
        private void button1_Click_1(object sender, EventArgs e)
        {
            byte[] start2a = new byte[3];
            start2a[0] = 0x38;
            start2a[1] = 0x01;
            start2a[2] = 00;
            WriteData(start2a);
        }
        private void button5_Click_1(object sender, EventArgs e)
        {
            byte[] start2a = new byte[2];
            start2a[0] = 0x12;
            start2a[1] = 0x00;
            WriteData(start2a);
        }
        private void btnGetArmValue_Click(object sender, EventArgs e)
        {
            string mystr = " ";

            mystr += caliData.calib_Points[0].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[1].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[2].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[3].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[4].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[5].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[6].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[7].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[8].y_value.ToString("f2");
            mystr += "   " + caliData.calib_Points[9].y_value.ToString("f2");
            txtLog.AppendText(mystr + "\r\n");
        }
        private void btnCheck_Click(object sender, EventArgs e)
        {
            float a = 0.0f;
            bool isblank = false;

            string str = "";
            float[] tmpArray = new float[10];
            a = (resultbyCalc[0] / 1000) * caliData.k_val + caliData.b_val + caliData.b_ajust;
            if (a > 0)
            {

                str = " ";

            }
            for (UInt32 i = 0; i < 10; i++)
            {

                a = (resultbyCalc[i] / 1000) * caliData.k_val + caliData.b_val + caliData.b_ajust;
                tmpArray[i] = a - caliData.calib_Points[i].y_value;
                if (a > 0)
                {
                    if (isblank)
                    {
                        str += " " + a.ToString("f2") + "   ";
                        isblank = false;
                    }
                    else
                    {
                        if (a >= 10)
                        {
                            str += a.ToString("f2") + "    ";
                        }
                        else
                        {
                            str += a.ToString("f2") + "   ";
                        }
                    }

                }
                else
                {
                    isblank = true;
                    str += a.ToString("f2") + "  ";
                }
            }
            txtLog.AppendText(str + "\r\n");
            str = "";
            if (tmpArray[0] > 0)
            {
                str = " ";
            }
            for (UInt32 i = 0; i < 10; i++)
            {
                if (tmpArray[i] >= 0)
                {
                    if (isblank)
                    {
                        str += " " + tmpArray[i].ToString("f2") + "   ";
                        isblank = false;
                    }
                    else
                    {
                        str += " " + tmpArray[i].ToString("f2") + " ";
                    }
                }
                else
                {
                    if (tmpArray[i] >= 10)
                    {
                        str += tmpArray[i].ToString("f2") + "   ";
                    }
                    else
                    {
                        isblank = true;
                        str += tmpArray[i].ToString("f2") + "   ";
                    }

                }
            }
            txtLog.AppendText(str + "\r\n");

        }
        float[] farraya = { 1.10f, 2.37f, 4.50f, 9.37f, 12.90f, 12.90f, 16.83f, 21.53f, 26.20f, 27.47f };
        float[] farrayb = { -0.27f, 1.62f, 4.45f, 9.17f, 12.95f, -12.95f, 16.73f, 21.45f, 26.17f, 26.17f };
        //正常校准（移除）
        //private void btnWhite_Click(object sender, EventArgs e)
        //{
        //    set_time_stamp();
        //    Thread.Sleep(300);
        //    DevLockOrUnlock(false);
        //    Thread.Sleep(300);
        //    // UInt32 ttimestamp = 0;
        //    askCali((byte)0x02);
        //}
        //private async void button1_Click(object sender, EventArgs e)
        //{
        //    float k;
        //    float b;
        //    k = (float)0.9709;
        //    b = (float)-2.181;
        //    Sendkb(k, b);
        //}
        private void Sendkb(float k, float b)//0x32
        {
            byte[] mybyte = new byte[] { (byte)0x32, (byte)0x06, (byte)0x00, (byte)0x01, (byte)0x02, (byte)0x03, (byte)0x04, (byte)0x05 };
            UInt32 aa, bb;
            if (k >= 0)
            {
                mybyte[2] = 1;
            }
            else
            {
                mybyte[2] = 0;
            }
            aa = (UInt32)(Math.Abs(k * 10000));
            mybyte[3] = (byte)(aa >> 8);
            mybyte[4] = (byte)(aa & 0xff);
            if (b >= 0)
            {
                mybyte[5] = 1;
            }
            else
            {
                mybyte[5] = 0;
            }
            bb = (UInt32)(Math.Abs(b * 10000)); //0x5532
            mybyte[6] = (byte)(bb >> 8); //0x55    85
            mybyte[7] = (byte)(bb & 0xff); //0x32   
            WriteData(mybyte);
            Thread.Sleep(1000);
            WriteData(mybyte);
        }
        //可用于时间戳转换为byte
        public byte[] intToBytes(int value)
        {
            byte[] src = new byte[4];
            src[3] = (byte)((value >> 24) & 0xFF);
            src[2] = (byte)((value >> 16) & 0xFF);
            src[1] = (byte)((value >> 8) & 0xFF);//高8位
            src[0] = (byte)(value & 0xFF);//低位
            return src;
        }
        private void CalGetVal(Int32 channal)//0x30
        {
            byte[] mybyte = new byte[] { (byte)0x30, (byte)0x01, (byte)0x01 };
            mybyte[2] = (byte)channal;
            WriteData(mybyte);
        }
    }
}
