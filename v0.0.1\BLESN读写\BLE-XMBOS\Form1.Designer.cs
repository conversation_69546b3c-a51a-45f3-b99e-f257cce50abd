﻿//using OfficeOpenXml;
using System;
using System.Data;
using System.Drawing;
using System.Linq.Expressions;
using System.Windows.Forms;
using static System.Net.Mime.MediaTypeNames;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;

namespace YSJ_10Cali
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码
        // 辅助：生成占位用的 PictureBox
        private PictureBox NewPlaceholderPicture()
        {
            return new PictureBox
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                SizeMode = PictureBoxSizeMode.CenterImage
                // 这里后续可以通过 pictureBox.Image = yourFormulaBitmap 来加载公式图
            };
        }
        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form1));
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.btnAdd5GPWM = new System.Windows.Forms.Button();
            this.btnSub1GPWM = new System.Windows.Forms.Button();
            this.lbSetRatio_CH = new System.Windows.Forms.Label();
            this.btnAdd1Ratio = new System.Windows.Forms.Button();
            this.txtCalResult = new System.Windows.Forms.TextBox();
            this.btnAdd5Ratio = new System.Windows.Forms.Button();
            this.lbCalResult = new System.Windows.Forms.Label();
            this.btnSub1Ratio = new System.Windows.Forms.Button();
            this.txtCalState = new System.Windows.Forms.TextBox();
            this.btnSub5Ratio = new System.Windows.Forms.Button();
            this.lbCalState = new System.Windows.Forms.Label();
            this.txtBPWM = new System.Windows.Forms.TextBox();
            this.lbADJRatio = new System.Windows.Forms.Label();
            this.btnSetRatio = new System.Windows.Forms.Button();
            this.lbBPWM_CH = new System.Windows.Forms.Label();
            this.txtShowSetRatio = new System.Windows.Forms.TextBox();
            this.txtGPWM = new System.Windows.Forms.TextBox();
            this.lbGPWM_CH = new System.Windows.Forms.Label();
            this.btnExCalRecord = new System.Windows.Forms.Button();
            this.btnSetGPWM = new System.Windows.Forms.Button();
            this.lbSetGPWM_CH = new System.Windows.Forms.Label();
            this.btnAutoCali = new System.Windows.Forms.Button();
            this.txtGPWMSetShow = new System.Windows.Forms.TextBox();
            this.btnEnterDarkCal = new System.Windows.Forms.Button();
            this.btnSub5GPWM = new System.Windows.Forms.Button();
            this.btnEnterWhiteCal = new System.Windows.Forms.Button();
            this.btnAdd1GPWM = new System.Windows.Forms.Button();
            this.btnEnterCalMode = new System.Windows.Forms.Button();
            this.tableCaliParamView = new System.Windows.Forms.DataGridView();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.lbFirmwareVer = new System.Windows.Forms.Label();
            this.lbFirmwareVer_CH = new System.Windows.Forms.Label();
            this.lbMacAddr = new System.Windows.Forms.Label();
            this.lbMacAddr_CH = new System.Windows.Forms.Label();
            this.txtRemainTime = new System.Windows.Forms.TextBox();
            this.lbRemainTime = new System.Windows.Forms.Label();
            this.txtStamp = new System.Windows.Forms.TextBox();
            this.lbStamp = new System.Windows.Forms.Label();
            this.btnAutoLock = new System.Windows.Forms.Button();
            this.txtAutoLockDays = new System.Windows.Forms.TextBox();
            this.lbAutoLockDays = new System.Windows.Forms.Label();
            this.ckboxENStamp = new System.Windows.Forms.CheckBox();
            this.btnReadRemainTime = new System.Windows.Forms.Button();
            this.btnMeasureOnce = new System.Windows.Forms.Button();
            this.btnLock = new System.Windows.Forms.Button();
            this.btnWStamp = new System.Windows.Forms.Button();
            this.lbDevState = new System.Windows.Forms.Label();
            this.btnWSN = new System.Windows.Forms.Button();
            this.txtSN = new System.Windows.Forms.TextBox();
            this.lbSN_CH = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.txtSignStress = new System.Windows.Forms.TextBox();
            this.btnScan = new System.Windows.Forms.Button();
            this.btnConn = new System.Windows.Forms.Button();
            this.txtDevName = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.lbE01LogPageNum = new System.Windows.Forms.Label();
            this.txtE01LogNowPage = new System.Windows.Forms.TextBox();
            this.btnAdd1E01LogPage = new System.Windows.Forms.Button();
            this.btnSub1E01LogPage = new System.Windows.Forms.Button();
            this.btnReadE01Log = new System.Windows.Forms.Button();
            this.btnDelE01Log = new System.Windows.Forms.Button();
            this.btnReadHisRecord = new System.Windows.Forms.Button();
            this.txtLog = new System.Windows.Forms.TextBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.btnExTestRecord = new System.Windows.Forms.Button();
            this.btnAutoTest = new System.Windows.Forms.Button();
            this.btnTest32mgColorBlock = new System.Windows.Forms.Button();
            this.btnTest10mgColorBlock = new System.Windows.Forms.Button();
            this.btnTest0mgColorBlock = new System.Windows.Forms.Button();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.tableTestRecord = new System.Windows.Forms.TableLayoutPanel();
            this.lbSample = new System.Windows.Forms.Label();
            this.lbRange = new System.Windows.Forms.Label();
            this.lbRange1 = new System.Windows.Forms.Label();
            this.lbRange2 = new System.Windows.Forms.Label();
            this.lbAcc = new System.Windows.Forms.Label();
            this.lbAcc1 = new System.Windows.Forms.Label();
            this.lbAcc2 = new System.Windows.Forms.Label();
            this.lbAccFormula = new System.Windows.Forms.Label();
            this.lbCV = new System.Windows.Forms.Label();
            this.lbCV1 = new System.Windows.Forms.Label();
            this.lbCV2 = new System.Windows.Forms.Label();
            this.lbCVFormula = new System.Windows.Forms.Label();
            this.lbNum1 = new System.Windows.Forms.Label();
            this.lbNum2 = new System.Windows.Forms.Label();
            this.lbNum3 = new System.Windows.Forms.Label();
            this.lbNum4 = new System.Windows.Forms.Label();
            this.lbNum5 = new System.Windows.Forms.Label();
            this.lbNum6 = new System.Windows.Forms.Label();
            this.lbNum7 = new System.Windows.Forms.Label();
            this.lbNum8 = new System.Windows.Forms.Label();
            this.lbNum9 = new System.Windows.Forms.Label();
            this.lbNum10 = new System.Windows.Forms.Label();
            this.lbFormat1 = new System.Windows.Forms.Label();
            this.lbFormat2 = new System.Windows.Forms.Label();
            this.p1 = new System.Windows.Forms.PictureBox();
            this.p2 = new System.Windows.Forms.PictureBox();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.tableTestResult = new System.Windows.Forms.TableLayoutPanel();
            this.lbTestResultRange = new System.Windows.Forms.Label();
            this.lbTestResultRange1 = new System.Windows.Forms.Label();
            this.lbTestResultRange2 = new System.Windows.Forms.Label();
            this.lbTestResultAcc1 = new System.Windows.Forms.Label();
            this.lbTestResultAcc = new System.Windows.Forms.Label();
            this.lbTestResultAcc2 = new System.Windows.Forms.Label();
            this.lbTestResultCV = new System.Windows.Forms.Label();
            this.lbTestResultCV1 = new System.Windows.Forms.Label();
            this.lbTestResultCV2 = new System.Windows.Forms.Label();
            this.lbTestResult = new System.Windows.Forms.Label();
            this.txtTestResult = new System.Windows.Forms.TextBox();
            this.lasResult = new Label[6];
            this.lasResult[0] = new Label(); this.lasResult[1] = new Label(); this.lasResult[2] = new Label();
            this.lasResult[3] = new Label(); this.lasResult[4] = new Label(); this.lasResult[5] = new Label();
            this.col0 = new System.Windows.Forms.DataGridViewColumn();
            this.col1 = new System.Windows.Forms.DataGridViewColumn();
            this.col2 = new System.Windows.Forms.DataGridViewColumn();
            this.col3 = new System.Windows.Forms.DataGridViewColumn();
            this.col4 = new System.Windows.Forms.DataGridViewColumn();
            this.col5 = new System.Windows.Forms.DataGridViewColumn();
            this.col6 = new System.Windows.Forms.DataGridViewColumn();
            this.las0_1 = new Label[10]; this.las0_1[0] = new Label(); this.las0_1[1] = new Label(); this.las0_1[2] = new Label();
            this.las0_1[3] = new Label(); this.las0_1[4] = new Label(); this.las0_1[5] = new Label(); this.las0_1[6] = new Label();
            this.las0_1[7] = new Label(); this.las0_1[8] = new Label(); this.las0_1[9] = new Label();
            this.las0_3 = new Label[10]; this.las0_3[0] = new Label(); this.las0_3[1] = new Label(); this.las0_3[2] = new Label();
            this.las0_3[3] = new Label(); this.las0_3[4] = new Label(); this.las0_3[5] = new Label(); this.las0_3[6] = new Label();
            this.las0_3[7] = new Label(); this.las0_3[8] = new Label(); this.las0_3[9] = new Label();
            this.las0_6 = new Label[10]; this.las0_6[0] = new Label(); this.las0_6[1] = new Label(); this.las0_6[2] = new Label();
            this.las0_6[3] = new Label(); this.las0_6[4] = new Label(); this.las0_6[5] = new Label(); this.las0_6[6] = new Label();
            this.las0_6[7] = new Label(); this.las0_6[8] = new Label(); this.las0_6[9] = new Label();
            this.las10_4 = new Label[10]; this.las10_4[0] = new Label(); this.las10_4[1] = new Label(); this.las10_4[2] = new Label();
            this.las10_4[3] = new Label(); this.las10_4[4] = new Label(); this.las10_4[5] = new Label(); this.las10_4[6] = new Label();
            this.las10_4[7] = new Label(); this.las10_4[8] = new Label(); this.las10_4[9] = new Label();
            this.las10_7 = new Label[10]; this.las10_7[0] = new Label(); this.las10_7[1] = new Label(); this.las10_7[2] = new Label();
            this.las10_7[3] = new Label(); this.las10_7[4] = new Label(); this.las10_7[5] = new Label(); this.las10_7[6] = new Label();
            this.las10_7[7] = new Label(); this.las10_7[8] = new Label(); this.las10_7[9] = new Label();
            this.las32 = new Label[10]; this.las32[0] = new Label(); this.las32[1] = new Label(); this.las32[2] = new Label();
            this.las32[3] = new Label(); this.las32[4] = new Label(); this.las32[5] = new Label(); this.las32[6] = new Label();
            this.las32[7] = new Label(); this.las32[8] = new Label(); this.las32[9] = new Label();
            this.lasTestResult = new Label[6]; this.lasTestResult[0] = new Label(); this.lasTestResult[1] = new Label();
            this.lasTestResult[2] = new Label(); this.lasTestResult[3] = new Label(); this.lasTestResult[4] = new Label(); this.lasTestResult[5] = new Label();
            //this.las[10] = new Label();
            this.timerBleScan = new System.Windows.Forms.Timer(this.components);
            // 移除菜单栏，改为顶端分组框样式
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tableCaliParamView)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.groupBox8.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainer1 (已弃用，不再添加到 Controls)
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.None;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.AutoScroll = true; // 启用自动滚动
            // 已迁移到 TabPages
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.AutoScroll = true;
            // 已迁移到 TabPages
            this.splitContainer1.Size = new System.Drawing.Size(0, 0);
            // 已不再使用 splitContainer1
            // 
            // tabControlMain and TabPages
            // 
            this.tabControlMain = new System.Windows.Forms.TabControl();
            this.tabPageConnection = new System.Windows.Forms.TabPage();
            this.tabPageCalibration = new System.Windows.Forms.TabPage();
            this.tabPageTestControl = new System.Windows.Forms.TabPage();
            this.tabPageTestRecord = new System.Windows.Forms.TabPage();
            this.tabPageTestResult = new System.Windows.Forms.TabPage();

            this.tabControlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlMain.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.tabControlMain.Name = "tabControlMain";
            this.tabControlMain.TabIndex = 100;
            this.tabControlMain.Controls.Add(this.tabPageConnection);
            this.tabControlMain.Controls.Add(this.tabPageCalibration);
            this.tabControlMain.Controls.Add(this.tabPageTestControl);
            this.tabControlMain.Controls.Add(this.tabPageTestRecord);
            this.tabControlMain.Controls.Add(this.tabPageTestResult);

            // 状态栏
            this.statusStripMain = new System.Windows.Forms.StatusStrip();
            this.lblCurrentUser = new System.Windows.Forms.Label();
            this.btnLogout = new System.Windows.Forms.Button();
            
            // 设置状态栏
            this.statusStripMain.BackColor = System.Drawing.Color.LightBlue;
            this.statusStripMain.Height = 40;
            this.statusStripMain.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.statusStripMain.Renderer = new System.Windows.Forms.ToolStripProfessionalRenderer();
            this.statusStripMain.SizingGrip = false;
            this.statusStripMain.Stretch = true;
            this.statusStripMain.BackgroundImage = null;
            this.statusStripMain.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            
            // 设置当前用户标签（保留用于兼容性）
            this.lblCurrentUser.Text = "当前用户：未登录";
            this.lblCurrentUser.Font = new System.Drawing.Font("Microsoft YaHei", 10);
            this.lblCurrentUser.AutoSize = true;
            
            // 设置退出按钮（保留用于兼容性）
            this.btnLogout.Text = "退出";
            this.btnLogout.Font = new System.Drawing.Font("Microsoft YaHei", 9);
            this.btnLogout.Size = new System.Drawing.Size(60, 25);
            this.btnLogout.Click += new System.EventHandler(this.btnLogout_Click);
            
            // 创建状态栏项
            this.statusLabelUser = new System.Windows.Forms.ToolStripStatusLabel();
            this.statusLabelUser.Text = "当前用户：未登录";
            this.statusLabelUser.Font = new System.Drawing.Font("Microsoft YaHei", 10, System.Drawing.FontStyle.Bold);
            this.statusLabelUser.ToolTipText = "当前登录用户信息";
            this.statusLabelUser.ForeColor = System.Drawing.Color.DarkGreen;
            
            this.statusButtonLogout = new System.Windows.Forms.ToolStripButton();
            this.statusButtonLogout.Text = "退出";
            this.statusButtonLogout.Font = new System.Drawing.Font("Microsoft YaHei", 9, System.Drawing.FontStyle.Bold);
            this.statusButtonLogout.ToolTipText = "退出当前用户登录";
            this.statusButtonLogout.BackColor = System.Drawing.Color.LightCoral;
            this.statusButtonLogout.ForeColor = System.Drawing.Color.DarkRed;
            this.statusButtonLogout.Click += new System.EventHandler(this.btnLogout_Click);
            
            // 添加控件到状态栏
            this.statusStripMain.Items.Add(this.statusLabelUser);
            this.statusStripMain.Items.Add(new System.Windows.Forms.ToolStripSeparator());
            
            // 添加连接状态标签
            this.statusLabelConnection = new System.Windows.Forms.ToolStripStatusLabel();
            this.statusLabelConnection.Text = "设备状态：无";
            this.statusLabelConnection.Font = new System.Drawing.Font("Microsoft YaHei", 9, System.Drawing.FontStyle.Bold);
            this.statusLabelConnection.ToolTipText = "显示当前蓝牙设备连接状态";
            this.statusLabelConnection.ForeColor = System.Drawing.Color.Red;
            this.statusStripMain.Items.Add(this.statusLabelConnection);
            
            this.statusStripMain.Items.Add(new System.Windows.Forms.ToolStripSeparator());
            
            // 添加
            // 状态标签
            this.statusLabelLockStatus = new System.Windows.Forms.ToolStripStatusLabel();
            this.statusLabelLockStatus.Text = "加锁状态：无";
            this.statusLabelLockStatus.Font = new System.Drawing.Font("Microsoft YaHei", 9, System.Drawing.FontStyle.Bold);
            this.statusLabelLockStatus.ToolTipText = "显示当前设备加锁状态";
            this.statusLabelLockStatus.ForeColor = System.Drawing.Color.Gray;
            this.statusStripMain.Items.Add(this.statusLabelLockStatus);
            
            this.statusStripMain.Items.Add(new System.Windows.Forms.ToolStripSeparator());
            
            // 添加当前时间标签
            this.statusLabelTime = new System.Windows.Forms.ToolStripStatusLabel();
            this.statusLabelTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            this.statusLabelTime.Font = new System.Drawing.Font("Microsoft YaHei", 9, System.Drawing.FontStyle.Bold);
            this.statusLabelTime.ToolTipText = "当前系统时间";
            this.statusLabelTime.ForeColor = System.Drawing.Color.DarkBlue;
            this.statusStripMain.Items.Add(this.statusLabelTime);
            
            // 添加弹性空间，让退出按钮靠右显示
            var elasticLabel = new System.Windows.Forms.ToolStripStatusLabel();
            elasticLabel.Spring = true;
            this.statusStripMain.Items.Add(elasticLabel);
            this.statusStripMain.Items.Add(new System.Windows.Forms.ToolStripSeparator());
            this.statusStripMain.Items.Add(this.statusButtonLogout);
            
            // 将状态栏添加到窗体
            this.Controls.Add(this.statusStripMain);
            
            // 创建定时器更新时间
            this.timerUpdateTime = new System.Windows.Forms.Timer();
            this.timerUpdateTime.Interval = 1000; // 1秒更新一次
            this.timerUpdateTime.Tick += new System.EventHandler(this.timerUpdateTime_Tick);
            this.timerUpdateTime.Start();

            // 连接与配置页
            this.tabPageConnection.Name = "tabPageConnection";
            this.tabPageConnection.Text = "连接与配置";
            this.tabPageConnection.UseVisualStyleBackColor = true;
            this.tabPageConnection.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageConnection.Controls.Add(this.groupBox3);
            this.tabPageConnection.Controls.Add(this.groupBox2);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Fill;

            // 设备校准页
            this.tabPageCalibration.Name = "tabPageCalibration";
            this.tabPageCalibration.Text = "设备校准";
            this.tabPageCalibration.UseVisualStyleBackColor = true;
            this.tabPageCalibration.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageCalibration.Controls.Add(this.groupBox5);
            this.groupBox5.Dock = System.Windows.Forms.DockStyle.Fill;

            // 检验控制页
            this.tabPageTestControl.Name = "tabPageTestControl";
            this.tabPageTestControl.Text = "检验控制";
            this.tabPageTestControl.UseVisualStyleBackColor = true;
            this.tabPageTestControl.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageTestControl.Controls.Add(this.groupBox4);
            this.groupBox4.Dock = System.Windows.Forms.DockStyle.Top;

            // 检验记录页
            this.tabPageTestRecord.Name = "tabPageTestRecord";
            this.tabPageTestRecord.Text = "检验记录";
            this.tabPageTestRecord.UseVisualStyleBackColor = true;
            this.tabPageTestRecord.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageTestRecord.Controls.Add(this.groupBox6);
            this.groupBox6.Dock = System.Windows.Forms.DockStyle.Fill;

            // 检验结果页
            this.tabPageTestResult.Name = "tabPageTestResult";
            this.tabPageTestResult.Text = "检验结果";
            this.tabPageTestResult.UseVisualStyleBackColor = true;
            this.tabPageTestResult.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageTestResult.Controls.Add(this.groupBox7);
            this.groupBox7.Dock = System.Windows.Forms.DockStyle.Fill;

            // 日志页已移除
            // 将日志固定在底部，不再放在单独 Tab 中
            this.panelLog = new System.Windows.Forms.Panel();
            this.panelLog.Dock = System.Windows.Forms.DockStyle.Right;
            this.panelLog.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.panelLog.Name = "panelLog";
            this.panelLog.Size = new System.Drawing.Size(600, 0);
            // 顶部"设备日志"按钮风格区域
            this.groupBoxLogTop = new System.Windows.Forms.GroupBox();
            this.groupBoxLogTop.Text = "设备日志";
            this.groupBoxLogTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBoxLogTop.Padding = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.groupBoxLogTop.Height = 70;

            this.btnReadHisRecordTop = new System.Windows.Forms.Button();
            this.btnReadHisRecordTop.Text = "读取历史记录";
            this.btnReadHisRecordTop.Location = new System.Drawing.Point(10, 25);
            this.btnReadHisRecordTop.Size = new System.Drawing.Size(120, 35);
            this.btnReadHisRecordTop.Click += new System.EventHandler(this.btnReadHisRecord_Click);

            this.btnDelE01LogTop = new System.Windows.Forms.Button();
            this.btnDelE01LogTop.Text = "擦除历史记录";
            this.btnDelE01LogTop.Location = new System.Drawing.Point(140, 25);
            this.btnDelE01LogTop.Size = new System.Drawing.Size(120, 35);
            this.btnDelE01LogTop.Click += new System.EventHandler(this.btnDelE01Log_Click);

            this.btnReadE01LogTop = new System.Windows.Forms.Button();
            this.btnReadE01LogTop.Text = "读取错误日志";
            this.btnReadE01LogTop.Location = new System.Drawing.Point(270, 25);
            this.btnReadE01LogTop.Size = new System.Drawing.Size(120, 35);
            this.btnReadE01LogTop.Click += new System.EventHandler(this.btnReadE01Log_Click);

            this.lbE01LogPageTop = new System.Windows.Forms.Label();
            this.lbE01LogPageTop.Text = "页:";
            this.lbE01LogPageTop.Location = new System.Drawing.Point(410, 33);

            this.txtE01LogNowPageTop = new System.Windows.Forms.TextBox();
            this.txtE01LogNowPageTop.Text = this.txtE01LogNowPage.Text;
            this.txtE01LogNowPageTop.Location = new System.Drawing.Point(440, 29);
            this.txtE01LogNowPageTop.Width = 50;

            this.groupBoxLogTop.Controls.Add(this.btnReadHisRecordTop);
            this.groupBoxLogTop.Controls.Add(this.btnDelE01LogTop);
            this.groupBoxLogTop.Controls.Add(this.btnReadE01LogTop);
            this.groupBoxLogTop.Controls.Add(this.lbE01LogPageTop);
            this.groupBoxLogTop.Controls.Add(this.txtE01LogNowPageTop);

            this.groupBox8.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelLog.Controls.Add(this.groupBox8);
            this.panelLog.Controls.Add(this.groupBoxLogTop);
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.btnAdd5GPWM);
            this.groupBox5.Controls.Add(this.btnSub1GPWM);
            this.groupBox5.Controls.Add(this.lbSetRatio_CH);
            this.groupBox5.Controls.Add(this.btnAdd1Ratio);
            this.groupBox5.Controls.Add(this.txtCalResult);
            this.groupBox5.Controls.Add(this.btnAdd5Ratio);
            this.groupBox5.Controls.Add(this.lbCalResult);
            this.groupBox5.Controls.Add(this.btnSub1Ratio);
            this.groupBox5.Controls.Add(this.txtCalState);
            this.groupBox5.Controls.Add(this.btnSub5Ratio);
            this.groupBox5.Controls.Add(this.lbCalState);
            this.groupBox5.Controls.Add(this.txtBPWM);
            this.groupBox5.Controls.Add(this.lbADJRatio);
            this.groupBox5.Controls.Add(this.btnSetRatio);
            this.groupBox5.Controls.Add(this.lbBPWM_CH);
            this.groupBox5.Controls.Add(this.txtShowSetRatio);
            this.groupBox5.Controls.Add(this.txtGPWM);
            this.groupBox5.Controls.Add(this.lbGPWM_CH);
            this.groupBox5.Controls.Add(this.btnExCalRecord);
            this.groupBox5.Controls.Add(this.btnSetGPWM);
            this.groupBox5.Controls.Add(this.lbSetGPWM_CH);
            this.groupBox5.Controls.Add(this.btnAutoCali);
            this.groupBox5.Controls.Add(this.txtGPWMSetShow);
            this.groupBox5.Controls.Add(this.btnEnterDarkCal);
            this.groupBox5.Controls.Add(this.btnSub5GPWM);
            this.groupBox5.Controls.Add(this.btnEnterWhiteCal);
            this.groupBox5.Controls.Add(this.btnAdd1GPWM);
            this.groupBox5.Controls.Add(this.btnEnterCalMode);
            this.groupBox5.Controls.Add(this.tableCaliParamView);
            this.groupBox5.Location = new System.Drawing.Point(6, 323);
            this.groupBox5.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Padding = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.groupBox5.Size = new System.Drawing.Size(900, 409);
            this.groupBox5.TabIndex = 38;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "设备校准";
            {
                // 
                // btnAdd5GPWM
                // 
                this.btnAdd5GPWM.Location = new System.Drawing.Point(429, 80);
                this.btnAdd5GPWM.Margin = new System.Windows.Forms.Padding(2);
                this.btnAdd5GPWM.Name = "btnAdd5GPWM";
                this.btnAdd5GPWM.Size = new System.Drawing.Size(56, 50);
                this.btnAdd5GPWM.TabIndex = 40;
                this.btnAdd5GPWM.Text = "+5";
                this.btnAdd5GPWM.UseVisualStyleBackColor = true;
                this.btnAdd5GPWM.Click += new System.EventHandler(this.btnAdd5GPWM_Click);
                // 
                // btnSub1GPWM
                // 
                this.btnSub1GPWM.Location = new System.Drawing.Point(196, 81);
                this.btnSub1GPWM.Margin = new System.Windows.Forms.Padding(2);
                this.btnSub1GPWM.Name = "btnSub1GPWM";
                this.btnSub1GPWM.Size = new System.Drawing.Size(59, 50);
                this.btnSub1GPWM.TabIndex = 39;
                this.btnSub1GPWM.Text = "-1";
                this.btnSub1GPWM.UseVisualStyleBackColor = true;
                this.btnSub1GPWM.Click += new System.EventHandler(this.btnSub1GPWM_Click);
                // 
                // lbSetRatio_CH
                // 
                this.lbSetRatio_CH.AutoSize = true;
                this.lbSetRatio_CH.Location = new System.Drawing.Point(27, 199);
                this.lbSetRatio_CH.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbSetRatio_CH.Name = "lbSetRatio_CH";
                this.lbSetRatio_CH.Size = new System.Drawing.Size(80, 18);
                this.lbSetRatio_CH.TabIndex = 38;
                this.lbSetRatio_CH.Text = "调整比率";
                // 
                // btnAdd1Ratio
                // 
                this.btnAdd1Ratio.Location = new System.Drawing.Point(369, 186);
                this.btnAdd1Ratio.Margin = new System.Windows.Forms.Padding(2);
                this.btnAdd1Ratio.Name = "btnAdd1Ratio";
                this.btnAdd1Ratio.Size = new System.Drawing.Size(56, 50);
                this.btnAdd1Ratio.TabIndex = 37;
                this.btnAdd1Ratio.Text = "+1";
                this.btnAdd1Ratio.UseVisualStyleBackColor = true;
                this.btnAdd1Ratio.Click += new System.EventHandler(this.btnAdd1Ratio_Click);
                // 
                // txtCalResult
                // 
                this.txtCalResult.Location = new System.Drawing.Point(567, 360);
                this.txtCalResult.Margin = new System.Windows.Forms.Padding(2);
                this.txtCalResult.Name = "txtCalResult";
                this.txtCalResult.Size = new System.Drawing.Size(56, 28);
                this.txtCalResult.TabIndex = 29;
                // 
                // btnAdd5Ratio
                // 
                this.btnAdd5Ratio.Location = new System.Drawing.Point(429, 186);
                this.btnAdd5Ratio.Margin = new System.Windows.Forms.Padding(2);
                this.btnAdd5Ratio.Name = "btnAdd5Ratio";
                this.btnAdd5Ratio.Size = new System.Drawing.Size(56, 50);
                this.btnAdd5Ratio.TabIndex = 36;
                this.btnAdd5Ratio.Text = "+5";
                this.btnAdd5Ratio.UseVisualStyleBackColor = true;
                this.btnAdd5Ratio.Click += new System.EventHandler(this.btnAdd5Ratio_Click);
                // 
                // lbCalResult
                // 
                this.lbCalResult.AutoSize = true;
                this.lbCalResult.Location = new System.Drawing.Point(471, 364);
                this.lbCalResult.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbCalResult.Name = "lbCalResult";
                this.lbCalResult.Size = new System.Drawing.Size(80, 18);
                this.lbCalResult.TabIndex = 28;
                this.lbCalResult.Text = "校准结果";
                // 
                // btnSub1Ratio
                // 
                this.btnSub1Ratio.Location = new System.Drawing.Point(196, 186);
                this.btnSub1Ratio.Margin = new System.Windows.Forms.Padding(2);
                this.btnSub1Ratio.Name = "btnSub1Ratio";
                this.btnSub1Ratio.Size = new System.Drawing.Size(59, 50);
                this.btnSub1Ratio.TabIndex = 35;
                this.btnSub1Ratio.Text = "-1";
                this.btnSub1Ratio.UseVisualStyleBackColor = true;
                this.btnSub1Ratio.Click += new System.EventHandler(this.btnSub1Ratio_Click);
                // 
                // txtCalState
                // 
                this.txtCalState.Location = new System.Drawing.Point(377, 360);
                this.txtCalState.Margin = new System.Windows.Forms.Padding(2);
                this.txtCalState.Name = "txtCalState";
                this.txtCalState.Size = new System.Drawing.Size(64, 28);
                this.txtCalState.TabIndex = 27;
                // 
                // btnSub5Ratio
                // 
                this.btnSub5Ratio.Location = new System.Drawing.Point(133, 186);
                this.btnSub5Ratio.Margin = new System.Windows.Forms.Padding(2);
                this.btnSub5Ratio.Name = "btnSub5Ratio";
                this.btnSub5Ratio.Size = new System.Drawing.Size(59, 50);
                this.btnSub5Ratio.TabIndex = 34;
                this.btnSub5Ratio.Text = "-5";
                this.btnSub5Ratio.UseVisualStyleBackColor = true;
                this.btnSub5Ratio.Click += new System.EventHandler(this.btnSub5Ratio_Click);
                // 
                // lbCalState
                // 
                this.lbCalState.AutoSize = true;
                this.lbCalState.Location = new System.Drawing.Point(270, 364);
                this.lbCalState.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbCalState.Name = "lbCalState";
                this.lbCalState.Size = new System.Drawing.Size(80, 18);
                this.lbCalState.TabIndex = 26;
                this.lbCalState.Text = "校准状态";

                // txtBPWM
                // 
                this.txtBPWM.Location = new System.Drawing.Point(207, 361);
                this.txtBPWM.Margin = new System.Windows.Forms.Padding(2);
                this.txtBPWM.Name = "txtBPWM";
                this.txtBPWM.Size = new System.Drawing.Size(43, 28);
                this.txtBPWM.TabIndex = 25;
                // 
                // lbADJRatio
                // 
                this.lbADJRatio.Font = new System.Drawing.Font("仿宋", 26.14286F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
                this.lbADJRatio.ForeColor = System.Drawing.SystemColors.HotTrack;
                this.lbADJRatio.Location = new System.Drawing.Point(250, 133);
                this.lbADJRatio.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbADJRatio.Name = "lbADJRatio";
                this.lbADJRatio.Size = new System.Drawing.Size(161, 65);
                this.lbADJRatio.TabIndex = 32;
                this.lbADJRatio.Text = "比率";
                // 
                // btnSetRatio
                // 
                this.btnSetRatio.Location = new System.Drawing.Point(509, 190);
                this.btnSetRatio.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnSetRatio.Name = "btnSetRatio";
                this.btnSetRatio.Size = new System.Drawing.Size(101, 42);
                this.btnSetRatio.TabIndex = 29;
                this.btnSetRatio.Text = "调整比率";
                this.btnSetRatio.UseVisualStyleBackColor = true;
                this.btnSetRatio.Click += new System.EventHandler(this.btnSetRatio_Click);
                // 
                // lbBPWM_CH
                // 
                this.lbBPWM_CH.AutoSize = true;
                this.lbBPWM_CH.Location = new System.Drawing.Point(141, 364);
                this.lbBPWM_CH.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbBPWM_CH.Name = "lbBPWM_CH";
                this.lbBPWM_CH.Size = new System.Drawing.Size(53, 18);
                this.lbBPWM_CH.TabIndex = 24;
                this.lbBPWM_CH.Text = "B-PWM";
                // 
                // txtShowSetRatio
                // 
                this.txtShowSetRatio.Location = new System.Drawing.Point(259, 199);
                this.txtShowSetRatio.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.txtShowSetRatio.Name = "txtShowSetRatio";
                this.txtShowSetRatio.Size = new System.Drawing.Size(106, 28);
                this.txtShowSetRatio.TabIndex = 28;
                // 
                // txtGPWM
                // 
                this.txtGPWM.Location = new System.Drawing.Point(79, 360);
                this.txtGPWM.Margin = new System.Windows.Forms.Padding(2);
                this.txtGPWM.Name = "txtGPWM";
                this.txtGPWM.Size = new System.Drawing.Size(43, 28);
                this.txtGPWM.TabIndex = 23;
                // 
                // lbGPWM_CH
                // 
                this.lbGPWM_CH.AutoSize = true;
                this.lbGPWM_CH.Location = new System.Drawing.Point(12, 364);
                this.lbGPWM_CH.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbGPWM_CH.Name = "lbGPWM_CH";
                this.lbGPWM_CH.Size = new System.Drawing.Size(53, 18);
                this.lbGPWM_CH.TabIndex = 22;
                this.lbGPWM_CH.Text = "G-PWM";
                // 
                // btnExCalRecord
                // 
                this.btnExCalRecord.Location = new System.Drawing.Point(617, 82);
                this.btnExCalRecord.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnExCalRecord.Name = "btnExCalRecord";
                this.btnExCalRecord.Size = new System.Drawing.Size(129, 46);
                this.btnExCalRecord.TabIndex = 17;
                this.btnExCalRecord.Text = "导出校准记录";
                this.btnExCalRecord.UseVisualStyleBackColor = true;
                this.btnExCalRecord.Click += new System.EventHandler(this.btnExCalRecord_Click);
                this.btnExCalRecord.Enabled = false;
                // 
                // btnSetGPWM
                // 
                this.btnSetGPWM.Location = new System.Drawing.Point(509, 82);
                this.btnSetGPWM.Margin = new System.Windows.Forms.Padding(2);
                this.btnSetGPWM.Name = "btnSetGPWM";
                this.btnSetGPWM.Size = new System.Drawing.Size(96, 50);
                this.btnSetGPWM.TabIndex = 16;
                this.btnSetGPWM.Text = "设置GPWM";
                this.btnSetGPWM.UseVisualStyleBackColor = true;
                this.btnSetGPWM.Click += new System.EventHandler(this.btnSetGPWM_Click);
                // 
                // lbSetGPWM_CH
                // 
                this.lbSetGPWM_CH.AutoSize = true;
                this.lbSetGPWM_CH.Location = new System.Drawing.Point(17, 96);
                this.lbSetGPWM_CH.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbSetGPWM_CH.Name = "lbSetGPWM_CH";
                this.lbSetGPWM_CH.Size = new System.Drawing.Size(80, 18);
                this.lbSetGPWM_CH.TabIndex = 15;
                this.lbSetGPWM_CH.Text = "调整GPWM";
                // 
                // btnAutoCali
                // 
                this.btnAutoCali.Enabled = false;
                this.btnAutoCali.Location = new System.Drawing.Point(617, 27);
                this.btnAutoCali.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnAutoCali.Name = "btnAutoCali";
                this.btnAutoCali.Size = new System.Drawing.Size(129, 51);
                this.btnAutoCali.TabIndex = 15;
                this.btnAutoCali.Text = "自动校准";
                this.btnAutoCali.UseVisualStyleBackColor = true;
                this.btnAutoCali.Click += new System.EventHandler(this.btnAutoCali_Click);
                // 
                // txtGPWMSetShow
                // 
                this.txtGPWMSetShow.Location = new System.Drawing.Point(259, 93);
                this.txtGPWMSetShow.Margin = new System.Windows.Forms.Padding(2);
                this.txtGPWMSetShow.Name = "txtGPWMSetShow";
                this.txtGPWMSetShow.Size = new System.Drawing.Size(106, 28);
                this.txtGPWMSetShow.TabIndex = 14;
                this.txtGPWMSetShow.Text = "180";
                // 
                // btnEnterDarkCal
                // 
                this.btnEnterDarkCal.Location = new System.Drawing.Point(270, 27);
                this.btnEnterDarkCal.Margin = new System.Windows.Forms.Padding(2);
                this.btnEnterDarkCal.Name = "btnEnterDarkCal";
                this.btnEnterDarkCal.Size = new System.Drawing.Size(99, 50);
                this.btnEnterDarkCal.TabIndex = 14;
                this.btnEnterDarkCal.Text = "暗电流校准";
                this.btnEnterDarkCal.UseVisualStyleBackColor = true;
                this.btnEnterDarkCal.Enabled = false;
                this.btnEnterDarkCal.Click += new System.EventHandler(this.btnEnterDarkCal_Click);
                // 
                // btnSub5GPWM
                // 
                this.btnSub5GPWM.Location = new System.Drawing.Point(133, 80);
                this.btnSub5GPWM.Margin = new System.Windows.Forms.Padding(2);
                this.btnSub5GPWM.Name = "btnSub5GPWM";
                this.btnSub5GPWM.Size = new System.Drawing.Size(59, 50);
                this.btnSub5GPWM.TabIndex = 12;
                this.btnSub5GPWM.Text = "-5";
                this.btnSub5GPWM.UseVisualStyleBackColor = true;
                this.btnSub5GPWM.Click += new System.EventHandler(this.btnSub5GPWM_Click);
                // 
                // btnEnterWhiteCal
                // 
                this.btnEnterWhiteCal.Location = new System.Drawing.Point(141, 27);
                this.btnEnterWhiteCal.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnEnterWhiteCal.Name = "btnEnterWhiteCal";
                this.btnEnterWhiteCal.Size = new System.Drawing.Size(106, 50);
                this.btnEnterWhiteCal.TabIndex = 13;
                this.btnEnterWhiteCal.Text = "0.0mg/dL校准色屏";
                this.btnEnterWhiteCal.UseVisualStyleBackColor = true;
                this.btnEnterWhiteCal.Enabled = false;
                this.btnEnterWhiteCal.Click += new System.EventHandler(this.btnEnterWhiteCal_Click);
                // 
                // btnAdd1GPWM
                // 
                this.btnAdd1GPWM.Location = new System.Drawing.Point(369, 82);
                this.btnAdd1GPWM.Margin = new System.Windows.Forms.Padding(2);
                this.btnAdd1GPWM.Name = "btnAdd1GPWM";
                this.btnAdd1GPWM.Size = new System.Drawing.Size(56, 50);
                this.btnAdd1GPWM.TabIndex = 13;
                this.btnAdd1GPWM.Text = "+1";
                this.btnAdd1GPWM.UseVisualStyleBackColor = true;
                this.btnAdd1GPWM.Click += new System.EventHandler(this.btnAdd1GPWM_Click);
                // 
                // btnEnterCalMode
                // 
                this.btnEnterCalMode.Location = new System.Drawing.Point(20, 26);
                this.btnEnterCalMode.Margin = new System.Windows.Forms.Padding(2);
                this.btnEnterCalMode.Name = "btnEnterCalMode";
                this.btnEnterCalMode.Size = new System.Drawing.Size(99, 51);
                this.btnEnterCalMode.TabIndex = 12;
                this.btnEnterCalMode.Text = "进入校准模式";
                this.btnEnterCalMode.UseVisualStyleBackColor = true;
                this.btnEnterCalMode.Click += new System.EventHandler(this.btnEnterCalMode_Click);
                // 
                // tableCaliParamView
                // 
                //this.tableCaliParamView.ColumnHeadersHeight = 34;
                this.tableCaliParamView.Location = new System.Drawing.Point(13, 237);
                this.tableCaliParamView.Margin = new System.Windows.Forms.Padding(2);
                this.tableCaliParamView.Name = "tableCaliParamView";
                this.tableCaliParamView.RowHeadersVisible = false;
                //this.tableCaliParamView.RowHeadersWidth = 62;
                this.tableCaliParamView.Size = new System.Drawing.Size(850, 100);
                this.tableCaliParamView.TabIndex = 12;
                {
                    DataGridViewCell cell = new DataGridViewTextBoxCell();
                    DataGridViewCell cell1 = new DataGridViewTextBoxCell();
                    DataGridViewCell cell2 = new DataGridViewTextBoxCell();
                    DataGridViewRow row1 = new DataGridViewRow();
                    DataGridViewRow row2 = new DataGridViewRow();
                    this.col0.HeaderText = "名称";
                    this.col0.CellTemplate = cell;
                    this.col0.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                    this.tableCaliParamView.Columns.Add(col0);
                    this.col1.HeaderText = "D0";
                    this.col1.CellTemplate = cell;
                    this.col1.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                    this.tableCaliParamView.Columns.Add(col1);
                    this.col2.HeaderText = "G0";
                    this.col2.CellTemplate = cell;
                    this.col2.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                    this.tableCaliParamView.Columns.Add(col2);
                    this.col3.HeaderText = "B0";
                    this.col3.CellTemplate = cell;
                    this.col3.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                    this.tableCaliParamView.Columns.Add(col3);
                    this.col4.HeaderText = "D1";
                    this.col4.CellTemplate = cell;
                    this.col4.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                    this.tableCaliParamView.Columns.Add(col4);
                    this.col5.HeaderText = "G1";
                    this.col5.CellTemplate = cell;
                    this.col5.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                    this.tableCaliParamView.Columns.Add(col5);
                    this.col6.HeaderText = "B1";
                    this.col6.CellTemplate = cell;
                    this.col6.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                    this.tableCaliParamView.Columns.Add(col6);
                    cell1.Value = "校准参数值";
                    row1.Cells.Add(cell1);
                    this.tableCaliParamView.Rows.Add(row1);
                    cell2.Value = "校准限值范围";
                    row2.Cells.Add(cell2);
                    this.tableCaliParamView.Rows.Add(row2);
                    this.tableCaliParamView.Rows[1].Cells[1].Value = "0-50";
                    this.tableCaliParamView.Rows[1].Cells[2].Value = "2600-3300";
                    this.tableCaliParamView.Rows[1].Cells[3].Value = "2600-3300";
                    this.tableCaliParamView.Rows[1].Cells[4].Value = "/";
                    this.tableCaliParamView.Rows[1].Cells[5].Value = "0-200";
                    this.tableCaliParamView.Rows[1].Cells[6].Value = "0-200";
                    this.tableCaliParamView.AllowUserToAddRows = false;//删除最后一行空白行
                }
            }
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.lbFirmwareVer);
            this.groupBox3.Controls.Add(this.lbFirmwareVer_CH);
            this.groupBox3.Controls.Add(this.lbMacAddr);
            this.groupBox3.Controls.Add(this.lbMacAddr_CH);
            this.groupBox3.Controls.Add(this.txtRemainTime);
            this.groupBox3.Controls.Add(this.lbRemainTime);
            this.groupBox3.Controls.Add(this.btnReadRemainTime);
            this.groupBox3.Controls.Add(this.btnMeasureOnce);
            this.groupBox3.Controls.Add(this.txtStamp);
            this.groupBox3.Controls.Add(this.lbStamp);
            this.groupBox3.Controls.Add(this.btnAutoLock);
            this.groupBox3.Controls.Add(this.lbAutoLockDays);
            this.groupBox3.Controls.Add(this.txtAutoLockDays);
            this.groupBox3.Controls.Add(this.ckboxENStamp);

            this.groupBox3.Controls.Add(this.btnLock);
            this.groupBox3.Controls.Add(this.btnWStamp);
            this.groupBox3.Controls.Add(this.lbDevState);
            this.groupBox3.Controls.Add(this.btnWSN);
            this.groupBox3.Controls.Add(this.txtSN);
            this.groupBox3.Controls.Add(this.lbSN_CH);
            this.groupBox3.Location = new System.Drawing.Point(6, 81);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(2);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(2);
            this.groupBox3.Size = new System.Drawing.Size(828, 232);
            this.groupBox3.TabIndex = 4;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "设备配置";
            {
                // 
                // lbFirmwareVer
                // 
                this.lbFirmwareVer.AutoSize = true;
                this.lbFirmwareVer.Location = new System.Drawing.Point(125, 33);
                this.lbFirmwareVer.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbFirmwareVer.Name = "lbFirmwareVer";
                this.lbFirmwareVer.Size = new System.Drawing.Size(62, 18);
                this.lbFirmwareVer.TabIndex = 1;
                this.lbFirmwareVer.Text = "label2";
                // 
                // lbFirmwareVer_CH
                // 
                this.lbFirmwareVer_CH.AutoSize = true;
                this.lbFirmwareVer_CH.Location = new System.Drawing.Point(10, 33);
                this.lbFirmwareVer_CH.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbFirmwareVer_CH.Name = "lbFirmwareVer_CH";
                this.lbFirmwareVer_CH.Size = new System.Drawing.Size(80, 18);
                this.lbFirmwareVer_CH.TabIndex = 0;
                this.lbFirmwareVer_CH.Text = "固件版本";
                // 
                // lbMacAddr
                // 
                this.lbMacAddr.AutoSize = true;
                this.lbMacAddr.Location = new System.Drawing.Point(496, 33);
                this.lbMacAddr.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbMacAddr.Name = "lbMacAddr";
                this.lbMacAddr.Size = new System.Drawing.Size(62, 18);
                this.lbMacAddr.TabIndex = 6;
                this.lbMacAddr.Text = "label4";
                // 
                // lbMacAddr_CH
                // 
                this.lbMacAddr_CH.AutoSize = true;
                this.lbMacAddr_CH.Location = new System.Drawing.Point(381, 33);
                this.lbMacAddr_CH.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbMacAddr_CH.Name = "lbMacAddr_CH";
                this.lbMacAddr_CH.Size = new System.Drawing.Size(71, 18);
                this.lbMacAddr_CH.TabIndex = 5;
                this.lbMacAddr_CH.Text = "MAC地址";
                // 
                // txtRemainTime
                // 
                this.txtRemainTime.Location = new System.Drawing.Point(123, 137);
                this.txtRemainTime.Margin = new System.Windows.Forms.Padding(2);
                this.txtRemainTime.Name = "txtRemainTime";
                this.txtRemainTime.Size = new System.Drawing.Size(204, 28);
                this.txtRemainTime.TabIndex = 21;
            // 
            // btnReadRemainTime
            // 
            this.btnReadRemainTime.Location = new System.Drawing.Point(340, 137);
            this.btnReadRemainTime.Margin = new System.Windows.Forms.Padding(2);
            this.btnReadRemainTime.Name = "btnReadRemainTime";
            this.btnReadRemainTime.Size = new System.Drawing.Size(150, 35);
            this.btnReadRemainTime.TabIndex = 22;
            this.btnReadRemainTime.Text = "读取剩余时间";
            this.btnReadRemainTime.UseVisualStyleBackColor = true;
            this.btnReadRemainTime.Click += new System.EventHandler(this.btnReadRemainTime_Click);
            // 
            // btnMeasureOnce
            // 
            this.btnMeasureOnce.Location = new System.Drawing.Point(510, 137);
            this.btnMeasureOnce.Margin = new System.Windows.Forms.Padding(2);
            this.btnMeasureOnce.Name = "btnMeasureOnce";
            this.btnMeasureOnce.Size = new System.Drawing.Size(100, 35);
            this.btnMeasureOnce.TabIndex = 23;
            this.btnMeasureOnce.Text = "测量一次";
            this.btnMeasureOnce.UseVisualStyleBackColor = true;
            this.btnMeasureOnce.Click += new System.EventHandler(this.btnMeasureOnce_Click);
                // 
                // lbRemainTime
                // 
                this.lbRemainTime.AutoSize = true;
                this.lbRemainTime.Location = new System.Drawing.Point(10, 146);
                this.lbRemainTime.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbRemainTime.Name = "lbRemainTime";
                this.lbRemainTime.Size = new System.Drawing.Size(80, 18);
                this.lbRemainTime.TabIndex = 20;
                this.lbRemainTime.Text = "剩余时间";
                // 
                // txtStamp
                // 
                this.txtStamp.Location = new System.Drawing.Point(123, 98);
                this.txtStamp.Margin = new System.Windows.Forms.Padding(2);
                this.txtStamp.Name = "txtStamp";
                this.txtStamp.Size = new System.Drawing.Size(204, 28);
                this.txtStamp.TabIndex = 19;
                // 
                // lbStamp
                // 
                this.lbStamp.AutoSize = true;
                this.lbStamp.Location = new System.Drawing.Point(10, 107);
                this.lbStamp.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbStamp.Name = "lbStamp";
                this.lbStamp.Size = new System.Drawing.Size(62, 18);
                this.lbStamp.TabIndex = 18;
                this.lbStamp.Text = "时间戳";
                // 
                // btnAutoLock
                // 
                this.btnAutoLock.Location = new System.Drawing.Point(490, 182);
                this.btnAutoLock.Margin = new System.Windows.Forms.Padding(2);
                this.btnAutoLock.Name = "btnAutoLock";
                this.btnAutoLock.Size = new System.Drawing.Size(150, 39);
                this.btnAutoLock.TabIndex = 16;
                this.btnAutoLock.Text = "自动上锁";
                this.btnAutoLock.UseVisualStyleBackColor = true;
                this.btnAutoLock.Click += new System.EventHandler(this.btnAutoLock_Click);
                // 
                // lbAutoLockDays
                // 
                this.lbAutoLockDays.AutoSize = true;
                this.lbAutoLockDays.Location = new System.Drawing.Point(490, 230);
                this.lbAutoLockDays.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbAutoLockDays.Name = "lbAutoLockDays";
                this.lbAutoLockDays.Size = new System.Drawing.Size(80, 18);
                this.lbAutoLockDays.TabIndex = 19;
                this.lbAutoLockDays.Text = "自动上锁天数";
                // 
                // txtAutoLockDays
                // 
                this.txtAutoLockDays.Location = new System.Drawing.Point(490, 250);
                this.txtAutoLockDays.Margin = new System.Windows.Forms.Padding(2);
                this.txtAutoLockDays.Name = "txtAutoLockDays";
                this.txtAutoLockDays.Size = new System.Drawing.Size(150, 28);
                this.txtAutoLockDays.TabIndex = 20;
                this.txtAutoLockDays.Text = "1";
                this.txtAutoLockDays.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
                // 
                // ckboxENStamp
                // 
                this.ckboxENStamp.AutoSize = true;
                this.ckboxENStamp.Checked = true;
                this.ckboxENStamp.CheckState = System.Windows.Forms.CheckState.Checked;
                this.ckboxENStamp.Location = new System.Drawing.Point(490, 106);
                this.ckboxENStamp.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.ckboxENStamp.Name = "ckboxENStamp";
                this.ckboxENStamp.Size = new System.Drawing.Size(124, 22);
                this.ckboxENStamp.TabIndex = 10;
                this.ckboxENStamp.Text = "使能时间戳";
                this.ckboxENStamp.UseVisualStyleBackColor = true;
                // 
                
                // 
                // btnLock
                // 
                this.btnLock.Location = new System.Drawing.Point(116, 182);
                this.btnLock.Margin = new System.Windows.Forms.Padding(2);
                this.btnLock.Name = "btnLock";
                this.btnLock.Size = new System.Drawing.Size(84, 39);
                this.btnLock.TabIndex = 9;
                this.btnLock.Text = "加锁";
                this.btnLock.UseVisualStyleBackColor = true;
                this.btnLock.Click += new System.EventHandler(this.btnLock_Click);
                // 
                // btnWStamp
                // 
                this.btnWStamp.Location = new System.Drawing.Point(617, 96);
                this.btnWStamp.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnWStamp.Name = "btnWStamp";
                this.btnWStamp.Size = new System.Drawing.Size(137, 40);
                this.btnWStamp.TabIndex = 8;
                this.btnWStamp.Text = "写入时间戳";
                this.btnWStamp.UseVisualStyleBackColor = true;
                this.btnWStamp.Click += new System.EventHandler(this.btnWStamp_Click);
                // 
                // lbDevState
                // 
                this.lbDevState.AutoSize = true;
                this.lbDevState.Location = new System.Drawing.Point(10, 192);
                this.lbDevState.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbDevState.Name = "lbDevState";
                this.lbDevState.Size = new System.Drawing.Size(80, 18);
                this.lbDevState.TabIndex = 7;
                this.lbDevState.Text = "设备状态";
                // 
                // btnWSN
                // 
                this.btnWSN.Location = new System.Drawing.Point(617, 54);
                this.btnWSN.Margin = new System.Windows.Forms.Padding(2);
                this.btnWSN.Name = "btnWSN";
                this.btnWSN.Size = new System.Drawing.Size(137, 39);
                this.btnWSN.TabIndex = 4;
                this.btnWSN.Text = "写入序列号";
                this.btnWSN.UseVisualStyleBackColor = true;
                this.btnWSN.Click += new System.EventHandler(this.btnWSN_Click);
                // 
                // txtSN
                // 
                this.txtSN.Location = new System.Drawing.Point(123, 61);
                this.txtSN.Margin = new System.Windows.Forms.Padding(2);
                this.txtSN.Name = "txtSN";
                this.txtSN.Size = new System.Drawing.Size(204, 28);
                this.txtSN.TabIndex = 3;
                // 
                // lbSN_CH
                // 
                this.lbSN_CH.AutoSize = true;
                this.lbSN_CH.Location = new System.Drawing.Point(10, 70);
                this.lbSN_CH.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbSN_CH.Name = "lbSN_CH";
                this.lbSN_CH.Size = new System.Drawing.Size(62, 18);
                this.lbSN_CH.TabIndex = 2;
                this.lbSN_CH.Text = "序列号";
            }
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.txtSignStress);
            this.groupBox2.Controls.Add(this.btnScan);
            this.groupBox2.Controls.Add(this.btnConn);
            this.groupBox2.Controls.Add(this.txtDevName);
            this.groupBox2.Location = new System.Drawing.Point(6, 0);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.groupBox2.Size = new System.Drawing.Size(828, 81);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "连接设备";
            {
                // 
                // txtSignStress
                // 
                this.txtSignStress.Location = new System.Drawing.Point(448, 30);
                this.txtSignStress.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.txtSignStress.Name = "txtSignStress";
                this.txtSignStress.Size = new System.Drawing.Size(40, 28);
                this.txtSignStress.TabIndex = 3;
                this.txtSignStress.Text = "4";
                this.txtSignStress.TextChanged += new System.EventHandler(this.txtTextChange);
                // 
                // btnScan
                // 
                this.btnScan.Anchor = (System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left);
                this.btnScan.Location = new System.Drawing.Point(250, 15);
                this.btnScan.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
                this.btnScan.Name = "btnScan";
                this.btnScan.Size = new System.Drawing.Size(150, 55);
                this.btnScan.TabIndex = 2;
                this.btnScan.Text = "扫描蓝牙";
                this.btnScan.UseVisualStyleBackColor = true;
                this.btnScan.Click += new System.EventHandler(this.btnScan_Click);
                // 
                // btnConn
                // 
                this.btnConn.Location = new System.Drawing.Point(520, 17);
                this.btnConn.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
                this.btnConn.Name = "btnConn";
                this.btnConn.Size = new System.Drawing.Size(150, 55);
                this.btnConn.TabIndex = 1;
                this.btnConn.Text = "连接";
                this.btnConn.UseVisualStyleBackColor = true;
                this.btnConn.Click += new System.EventHandler(this.btnConn_Click);
                // 
                // txtDevName
                // 
                this.txtDevName.Location = new System.Drawing.Point(9, 30);
                this.txtDevName.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
                this.txtDevName.Name = "txtDevName";
                this.txtDevName.Size = new System.Drawing.Size(245, 28);
                this.txtDevName.TabIndex = 0;
            }
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.lbE01LogPageNum);
            this.groupBox1.Controls.Add(this.txtE01LogNowPage);
            this.groupBox1.Controls.Add(this.btnAdd1E01LogPage);
            this.groupBox1.Controls.Add(this.btnSub1E01LogPage);
            this.groupBox1.Controls.Add(this.btnReadE01Log);
            this.groupBox1.Controls.Add(this.btnDelE01Log);
            this.groupBox1.Controls.Add(this.btnReadHisRecord);
            this.groupBox1.Location = new System.Drawing.Point(6, 0);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.groupBox1.Size = new System.Drawing.Size(800, 100);
            //this.groupBox1.AutoSize = true;
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "设备日志";
            {
                // 
                // lbE01LogPageNum
                // 
                this.lbE01LogPageNum.AutoSize = true;
                this.lbE01LogPageNum.Location = new System.Drawing.Point(692, 49);
                this.lbE01LogPageNum.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbE01LogPageNum.Name = "lbE01LogPageNum";
                this.lbE01LogPageNum.Size = new System.Drawing.Size(62, 18);
                this.lbE01LogPageNum.TabIndex = 31;
                this.lbE01LogPageNum.Text = "总页数";
                // 
                // txtE01LogNowPage
                // 
                this.txtE01LogNowPage.Location = new System.Drawing.Point(526, 46);
                this.txtE01LogNowPage.Margin = new System.Windows.Forms.Padding(2);
                this.txtE01LogNowPage.Name = "txtE01LogNowPage";
                this.txtE01LogNowPage.Text = "1";
                this.txtE01LogNowPage.Size = new System.Drawing.Size(56, 28);
                this.txtE01LogNowPage.TabIndex = 30;
                // 
                // btnAdd1E01LogPage
                // 
                this.btnAdd1E01LogPage.Location = new System.Drawing.Point(598, 29);
                this.btnAdd1E01LogPage.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnAdd1E01LogPage.Name = "btnAdd1E01LogPage";
                this.btnAdd1E01LogPage.Size = new System.Drawing.Size(63, 59);
                this.btnAdd1E01LogPage.TabIndex = 18;
                this.btnAdd1E01LogPage.Text = "+1";
                this.btnAdd1E01LogPage.UseVisualStyleBackColor = true;
                this.btnAdd1E01LogPage.Click += new System.EventHandler(this.btnAdd1E01LogPage_Click);
                // 
                // btnSub1E01LogPage
                // 
                this.btnSub1E01LogPage.Location = new System.Drawing.Point(448, 29);
                this.btnSub1E01LogPage.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnSub1E01LogPage.Name = "btnSub1E01LogPage";
                this.btnSub1E01LogPage.Size = new System.Drawing.Size(64, 59);
                this.btnSub1E01LogPage.TabIndex = 17;
                this.btnSub1E01LogPage.Text = "-1";
                this.btnSub1E01LogPage.UseVisualStyleBackColor = true;
                this.btnSub1E01LogPage.Click += new System.EventHandler(this.btnSub1E01LogPage_Click);
                // 
                // btnReadE01Log
                // 
                this.btnReadE01Log.Location = new System.Drawing.Point(300, 28);
                this.btnReadE01Log.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnReadE01Log.Name = "btnReadE01Log";
                this.btnReadE01Log.Size = new System.Drawing.Size(125, 59);
                this.btnReadE01Log.TabIndex = 16;
                this.btnReadE01Log.Text = "读取错误日志";
                this.btnReadE01Log.UseVisualStyleBackColor = true;
                this.btnReadE01Log.Click += new System.EventHandler(this.btnReadE01Log_Click);
                // 
                // btnDelE01Log
                // 
                this.btnDelE01Log.Location = new System.Drawing.Point(158, 28);
                this.btnDelE01Log.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnDelE01Log.Name = "btnDelE01Log";
                this.btnDelE01Log.Size = new System.Drawing.Size(125, 59);
                this.btnDelE01Log.TabIndex = 15;
                this.btnDelE01Log.Text = "擦除历史记录";
                this.btnDelE01Log.UseVisualStyleBackColor = true;
                this.btnDelE01Log.Click += new System.EventHandler(this.btnDelE01Log_Click);
                // 
                // btnReadHisRecord
                // 
                this.btnReadHisRecord.Location = new System.Drawing.Point(13, 28);
                this.btnReadHisRecord.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnReadHisRecord.Name = "btnReadHisRecord";
                this.btnReadHisRecord.Size = new System.Drawing.Size(125, 59);
                this.btnReadHisRecord.TabIndex = 14;
                this.btnReadHisRecord.Text = "读取历史记录";
                this.btnReadHisRecord.UseVisualStyleBackColor = true;
                this.btnReadHisRecord.Click += new System.EventHandler(this.btnReadHisRecord_Click);
            }
            // 
            // groupBox8
            //             
            this.groupBox8.Controls.Add(this.txtLog);
            this.groupBox8.Location = new System.Drawing.Point(6, 100);
            this.groupBox8.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Padding = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.groupBox8.Size = new System.Drawing.Size(800, 1600);
            this.groupBox8.TabIndex = 0;
            this.groupBox8.TabStop = false;
            // 
            // txtLog
            // 
            this.txtLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtLog.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.txtLog.Location = new System.Drawing.Point(5, 95);
            this.txtLog.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.txtLog.Multiline = true;
            this.txtLog.ScrollBars = ScrollBars.Vertical;
            this.txtLog.Name = "txtLog";
            //this.txtLog.Size = new System.Drawing.Size(800, 1500);
            this.txtLog.TabIndex = 0;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.btnExTestRecord);
            this.groupBox4.Controls.Add(this.btnAutoTest);
            this.groupBox4.Controls.Add(this.btnTest32mgColorBlock);
            this.groupBox4.Controls.Add(this.btnTest10mgColorBlock);
            this.groupBox4.Controls.Add(this.btnTest0mgColorBlock);
            this.groupBox4.Location = new System.Drawing.Point(0, 730);
            this.groupBox4.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Padding = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.groupBox4.Size = new System.Drawing.Size(900, 153);
            this.groupBox4.TabIndex = 38;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "设备检验";
            {
                // 
                // btnExTestRecord
                // 
                this.btnExTestRecord.Location = new System.Drawing.Point(535, 90);
                this.btnExTestRecord.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnExTestRecord.Name = "btnExTestRecord";
                this.btnExTestRecord.Size = new System.Drawing.Size(127, 46);
                this.btnExTestRecord.TabIndex = 22;
                this.btnExTestRecord.Text = "导出检验记录";
                this.btnExTestRecord.UseVisualStyleBackColor = true;
                this.btnExTestRecord.Click += new System.EventHandler(this.btnExTestRecord_Click);
                this.btnExTestRecord.Enabled = false;
                // 
                // btnAutoTest
                // 
                this.btnAutoTest.Enabled = true;
                this.btnAutoTest.Location = new System.Drawing.Point(535, 33);
                this.btnAutoTest.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnAutoTest.Name = "btnAutoTest";
                this.btnAutoTest.Size = new System.Drawing.Size(127, 51);
                this.btnAutoTest.TabIndex = 21;
                this.btnAutoTest.Text = "自动检验";
                this.btnAutoTest.UseVisualStyleBackColor = true;
                this.btnAutoTest.Click += new System.EventHandler(this.btnAutoTest_Click);
                // 
                // btnTest32mgColorBlock
                // 
                this.btnTest32mgColorBlock.Location = new System.Drawing.Point(259, 33);
                this.btnTest32mgColorBlock.Margin = new System.Windows.Forms.Padding(2);
                this.btnTest32mgColorBlock.Name = "btnTest32mgColorBlock";
                this.btnTest32mgColorBlock.Size = new System.Drawing.Size(105, 50);
                this.btnTest32mgColorBlock.TabIndex = 20;
                this.btnTest32mgColorBlock.Text = "32.0mg/dL校验色屏";
                this.btnTest32mgColorBlock.UseVisualStyleBackColor = true;
                this.btnTest32mgColorBlock.Click += new System.EventHandler(this.btnTest32mgColorBlock_Click);
                // 
                // btnTest10mgColorBlock
                // 
                this.btnTest10mgColorBlock.Location = new System.Drawing.Point(130, 33);
                this.btnTest10mgColorBlock.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
                this.btnTest10mgColorBlock.Name = "btnTest10mgColorBlock";
                this.btnTest10mgColorBlock.Size = new System.Drawing.Size(106, 50);
                this.btnTest10mgColorBlock.TabIndex = 19;
                this.btnTest10mgColorBlock.Text = "10.0mg/dL校验色屏";
                this.btnTest10mgColorBlock.UseVisualStyleBackColor = true;
                this.btnTest10mgColorBlock.Click += new System.EventHandler(this.btnTest10mgColorBlock_Click);
                // 
                // btnTest0mgColorBlock
                // 
                this.btnTest0mgColorBlock.Location = new System.Drawing.Point(9, 32);
                this.btnTest0mgColorBlock.Margin = new System.Windows.Forms.Padding(2);
                this.btnTest0mgColorBlock.Name = "btnTest0mgColorBlock";
                this.btnTest0mgColorBlock.Size = new System.Drawing.Size(99, 51);
                this.btnTest0mgColorBlock.TabIndex = 18;
                this.btnTest0mgColorBlock.Text = "0.0mg/dL校验色屏";
                this.btnTest0mgColorBlock.UseVisualStyleBackColor = true;
                this.btnTest0mgColorBlock.Click += new System.EventHandler(this.btnTest0mgColorBlock_Click);
            }
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.tableTestRecord);
            this.groupBox6.Location = new System.Drawing.Point(0, 890);
            this.groupBox6.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Padding = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.groupBox6.Size = new System.Drawing.Size(1750, 500);
            this.groupBox6.TabIndex = 38;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "校验记录";
            {
                // 
                // tableTestRecord
                // 
                this.tableTestRecord.Dock = DockStyle.Fill;
                this.tableTestRecord.Location = new System.Drawing.Point(9, 42);
                this.tableTestRecord.Margin = new System.Windows.Forms.Padding(2);
                this.tableTestRecord.Name = "tableTestRecord";
                this.tableTestRecord.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
                this.tableTestRecord.AutoScroll = true;
                this.tableTestRecord.ColumnCount = 9;
                this.tableTestRecord.RowCount = 12;
                this.tableTestRecord.Size = new System.Drawing.Size(1750, 450);
                // 
                // lbSample
                // 
                this.lbSample.TextAlign = ContentAlignment.MiddleCenter;
                this.lbSample.Dock = DockStyle.Fill;
                this.lbSample.Margin = new Padding(0);
                this.lbSample.Font = new Font("微软雅黑", 9);
                this.lbSample.Text = "样本值序号";
                // 
                // lbRange
                // 
                this.lbRange.TextAlign = ContentAlignment.MiddleCenter;
                this.lbRange.Dock = DockStyle.Fill;
                this.lbRange.Margin = new Padding(0);
                this.lbRange.Font = new Font("微软雅黑", 9);
                this.lbRange.Text = "测量范围\n0.0mg/dL~32.0mg/dL";
                // 
                // lbRange1
                // 
                this.lbRange1.TextAlign = ContentAlignment.MiddleCenter;
                this.lbRange1.Dock = DockStyle.Fill;
                this.lbRange1.Margin = new Padding(0);
                this.lbRange1.Font = new Font("微软雅黑", 9);
                this.lbRange1.Text = "0.0mg/dL 校验色屏";
                // 
                // lbRange2
                // 
                this.lbRange2.TextAlign = ContentAlignment.MiddleCenter;
                this.lbRange2.Dock = DockStyle.Fill;
                this.lbRange2.Margin = new Padding(0);
                this.lbRange2.Font = new Font("微软雅黑", 9);
                this.lbRange2.Text = "32.0mg/dL 校验色屏";
                // 
                // lbAcc
                // 
                this.lbAcc.TextAlign = ContentAlignment.MiddleCenter;
                this.lbAcc.Dock = DockStyle.Fill;
                this.lbAcc.Margin = new Padding(0);
                this.lbAcc.Font = new Font("微软雅黑", 9);
                this.lbAcc.Text = "测量精确度\n±1.5 mg/dL";
                // 
                // lbAcc1
                // 
                this.lbAcc1.TextAlign = ContentAlignment.MiddleCenter;
                this.lbAcc1.Dock = DockStyle.Fill;
                this.lbAcc1.Margin = new Padding(0);
                this.lbAcc1.Font = new Font("微软雅黑", 9);
                this.lbAcc1.Text = "0.0mg/dL 校验色屏";
                // 
                // lbAcc2
                // 
                this.lbAcc2.TextAlign = ContentAlignment.MiddleCenter;
                this.lbAcc2.Dock = DockStyle.Fill;
                this.lbAcc2.Margin = new Padding(0);
                this.lbAcc2.Font = new Font("微软雅黑", 9);
                this.lbAcc2.Text = "10.0mg/dL 校验色屏";
                // 
                // lbAccFormula
                // 
                this.lbAccFormula.TextAlign = ContentAlignment.MiddleCenter;
                this.lbAccFormula.Dock = DockStyle.Fill;
                this.lbAccFormula.Margin = new Padding(0);
                this.lbAccFormula.Font = new Font("微软雅黑", 9);
                this.lbAccFormula.Text = "测量准确度计算公式";
                // 
                // lbCV
                // 
                this.lbCV.TextAlign = ContentAlignment.MiddleCenter;
                this.lbCV.Dock = DockStyle.Fill;
                this.lbCV.Margin = new Padding(0);
                this.lbCV.Font = new Font("微软雅黑", 9);
                this.lbCV.Text = "重复性(变异系数 CV)\n<10%";
                // 
                // lbCV1
                // 
                this.lbCV1.TextAlign = ContentAlignment.MiddleCenter;
                this.lbCV1.Dock = DockStyle.Fill;
                this.lbCV1.Margin = new Padding(0);
                this.lbCV1.Font = new Font("微软雅黑", 9);
                this.lbCV1.Text = "0.0mg/dL 校验色屏";
                // 
                // lbCV2
                // 
                this.lbCV2.TextAlign = ContentAlignment.MiddleCenter;
                this.lbCV2.Dock = DockStyle.Fill;
                this.lbCV2.Margin = new Padding(0);
                this.lbCV2.Font = new Font("微软雅黑", 9);
                this.lbCV2.Text = "10.0mg/dL 校验色屏";
                // 
                // lbCVFormula
                // 
                this.lbCVFormula.TextAlign = ContentAlignment.MiddleCenter;
                this.lbCVFormula.Dock = DockStyle.Fill;
                this.lbCVFormula.Margin = new Padding(0);
                this.lbCVFormula.Font = new Font("微软雅黑", 9);
                this.lbCVFormula.Text = "重复性(变异系数 CV)\n计算公式";
                // 
                // lbNum1
                // 
                this.lbNum1.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum1.Dock = DockStyle.Fill;
                this.lbNum1.Margin = new Padding(0);
                this.lbNum1.Font = new Font("微软雅黑", 9);
                this.lbNum1.Text = "1";
                // 
                // lbNum2
                // 
                this.lbNum2.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum2.Dock = DockStyle.Fill;
                this.lbNum2.Margin = new Padding(0);
                this.lbNum2.Font = new Font("微软雅黑", 9);
                this.lbNum2.Text = "2";
                // 
                // lbNum3
                // 
                this.lbNum3.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum3.Dock = DockStyle.Fill;
                this.lbNum3.Margin = new Padding(0);
                this.lbNum3.Font = new Font("微软雅黑", 9);
                this.lbNum3.Text = "3";
                // 
                // lbNum4
                // 
                this.lbNum4.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum4.Dock = DockStyle.Fill;
                this.lbNum4.Margin = new Padding(0);
                this.lbNum4.Font = new Font("微软雅黑", 9);
                this.lbNum4.Text = "4";
                // 
                // lbNum5
                // 
                this.lbNum5.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum5.Dock = DockStyle.Fill;
                this.lbNum5.Margin = new Padding(0);
                this.lbNum5.Font = new Font("微软雅黑", 9);
                this.lbNum5.Text = "5";
                // 
                // lbNum6
                // 
                this.lbNum6.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum6.Dock = DockStyle.Fill;
                this.lbNum6.Margin = new Padding(0);
                this.lbNum6.Font = new Font("微软雅黑", 9);
                this.lbNum6.Text = "6";
                // 
                // lbNum7
                // 
                this.lbNum7.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum7.Dock = DockStyle.Fill;
                this.lbNum7.Margin = new Padding(0);
                this.lbNum7.Font = new Font("微软雅黑", 9);
                this.lbNum7.Text = "7";
                // 
                // lbNum8
                // 
                this.lbNum8.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum8.Dock = DockStyle.Fill;
                this.lbNum8.Margin = new Padding(0);
                this.lbNum8.Font = new Font("微软雅黑", 9);
                this.lbNum8.Text = "8";
                // 
                // lbNum9
                // 
                this.lbNum9.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum9.Dock = DockStyle.Fill;
                this.lbNum9.Margin = new Padding(0);
                this.lbNum9.Font = new Font("微软雅黑", 9);
                this.lbNum9.Text = "9";
                // 
                // lbNum10
                // 
                this.lbNum10.TextAlign = ContentAlignment.MiddleCenter;
                this.lbNum10.Dock = DockStyle.Fill;
                this.lbNum10.Margin = new Padding(0);
                this.lbNum10.Font = new Font("微软雅黑", 9);
                this.lbNum10.Text = "10";
                // 
                // lbFormat1
                // 
                this.lbFormat1.TextAlign = ContentAlignment.MiddleCenter;
                this.lbFormat1.Dock = DockStyle.Fill;
                this.lbFormat1.Margin = new Padding(0);
                this.lbFormat1.Font = new Font("微软雅黑", 9);
                this.lbFormat1.Text = "";
                // 
                // lbFormat2
                // 
                this.lbFormat2.TextAlign = ContentAlignment.MiddleCenter;
                this.lbFormat2.Dock = DockStyle.Fill;
                this.lbFormat2.Margin = new Padding(0);
                this.lbFormat2.Font = new Font("微软雅黑", 9);
                this.lbFormat2.Text = "";
                // 
                // p1
                // 
                this.p1.Dock = System.Windows.Forms.DockStyle.Fill;
                this.p1.Image = ((System.Drawing.Image)(resources.GetObject("p1.Image")));
                this.p1.Location = new System.Drawing.Point(509, 116);
                this.p1.Name = "p1";
                this.tableTestRecord.SetRowSpan(this.p1, 10);
                this.p1.Size = new System.Drawing.Size(400, 303);
                this.p1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
                this.p1.TabIndex = 22;
                this.p1.TabStop = false;
                // 
                // p2
                // 
                this.p2.Dock = System.Windows.Forms.DockStyle.Fill;
                this.p2.Image = ((System.Drawing.Image)(resources.GetObject("p2.Image")));
                this.p2.Location = new System.Drawing.Point(1222, 116);
                this.p2.Name = "p2";
                this.tableTestRecord.SetRowSpan(this.p2, 10);
                this.p2.Size = new System.Drawing.Size(400, 303);
                this.p2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
                this.p2.TabIndex = 23;
                this.p2.TabStop = false;
                // 设置列宽（可根据需要调整）
                ColumnStyle colStyle0 = new ColumnStyle(SizeType.Absolute, 80F);
                tableTestRecord.ColumnStyles.Add(colStyle0);    // 样本值序号
                ColumnStyle colStyle1 = new ColumnStyle(SizeType.Absolute, 100F);// 其余列平均分
                ColumnStyle colStyle2 = new ColumnStyle(SizeType.Absolute, 110F);
                ColumnStyle colStyle3 = new ColumnStyle(SizeType.Absolute, 100F);
                ColumnStyle colStyle4 = new ColumnStyle(SizeType.Absolute, 110F);
                ColumnStyle colStyle5 = new ColumnStyle(SizeType.Absolute, 400F);
                ColumnStyle colStyle6 = new ColumnStyle(SizeType.Absolute, 100F);
                ColumnStyle colStyle7 = new ColumnStyle(SizeType.Absolute, 110F);
                ColumnStyle colStyle8 = new ColumnStyle(SizeType.Absolute, 400F);
                tableTestRecord.ColumnStyles.Add(colStyle1);
                tableTestRecord.ColumnStyles.Add(colStyle2);
                tableTestRecord.ColumnStyles.Add(colStyle3);
                tableTestRecord.ColumnStyles.Add(colStyle4);
                tableTestRecord.ColumnStyles.Add(colStyle5);
                tableTestRecord.ColumnStyles.Add(colStyle6);
                tableTestRecord.ColumnStyles.Add(colStyle7);
                tableTestRecord.ColumnStyles.Add(colStyle8);

                RowStyle rowStyle0 = new RowStyle(SizeType.Absolute, 50F);
                RowStyle rowStyle1 = new RowStyle(SizeType.Absolute, 60F);
                tableTestRecord.RowStyles.Add(rowStyle0);// 行高固定
                tableTestRecord.RowStyles.Add(rowStyle1);
                RowStyle rowStyle2 = new RowStyle(SizeType.Absolute, 30F);// 数据行
                RowStyle rowStyle3 = new RowStyle(SizeType.Absolute, 30F);
                RowStyle rowStyle4 = new RowStyle(SizeType.Absolute, 30F);
                RowStyle rowStyle5 = new RowStyle(SizeType.Absolute, 30F);
                RowStyle rowStyle6 = new RowStyle(SizeType.Absolute, 30F);
                RowStyle rowStyle7 = new RowStyle(SizeType.Absolute, 30F);
                RowStyle rowStyle8 = new RowStyle(SizeType.Absolute, 30F);
                RowStyle rowStyle9 = new RowStyle(SizeType.Absolute, 30F);
                RowStyle rowStyle10 = new RowStyle(SizeType.Absolute, 30F);
                RowStyle rowStyle11 = new RowStyle(SizeType.Absolute, 30F);
                tableTestRecord.RowStyles.Add(rowStyle2);
                tableTestRecord.RowStyles.Add(rowStyle3);
                tableTestRecord.RowStyles.Add(rowStyle4);
                tableTestRecord.RowStyles.Add(rowStyle5);
                tableTestRecord.RowStyles.Add(rowStyle6);
                tableTestRecord.RowStyles.Add(rowStyle7);
                tableTestRecord.RowStyles.Add(rowStyle8);
                tableTestRecord.RowStyles.Add(rowStyle9);
                tableTestRecord.RowStyles.Add(rowStyle10);
                tableTestRecord.RowStyles.Add(rowStyle11);
                //// —— 第一行：一级表头 ——
                tableTestRecord.Controls.Add(lbSample, 0, 0);
                tableTestRecord.SetRowSpan(lbSample, 2);
                tableTestRecord.Controls.Add(lbRange, 1, 0);
                tableTestRecord.SetColumnSpan(lbRange, 2);
                tableTestRecord.Controls.Add(lbAcc, 3, 0);
                tableTestRecord.SetColumnSpan(lbAcc, 2);
                tableTestRecord.Controls.Add(lbAccFormula, 5, 0);
                tableTestRecord.SetRowSpan(lbAccFormula, 2);
                tableTestRecord.Controls.Add(lbCV, 6, 0);
                tableTestRecord.SetColumnSpan(lbCV, 2);
                tableTestRecord.Controls.Add(lbCVFormula, 8, 0);
                tableTestRecord.SetRowSpan(lbCVFormula, 2);

                //// —— 第二行：二级表头 ——
                tableTestRecord.Controls.Add(lbRange1, 1, 1);
                tableTestRecord.Controls.Add(lbRange2, 2, 1);
                tableTestRecord.Controls.Add(lbAcc1, 3, 1);
                tableTestRecord.Controls.Add(lbAcc2, 4, 1);
                tableTestRecord.Controls.Add(lbCV1, 6, 1);
                tableTestRecord.Controls.Add(lbCV2, 7, 1);

                // —— 后面 10 行：数据行，占位空白 —— 
                tableTestRecord.Controls.Add(lbNum1, 0, 2); tableTestRecord.Controls.Add(lbNum2, 0, 3); tableTestRecord.Controls.Add(lbNum3, 0, 4);
                tableTestRecord.Controls.Add(lbNum4, 0, 5); tableTestRecord.Controls.Add(lbNum5, 0, 6); tableTestRecord.Controls.Add(lbNum6, 0, 7);
                tableTestRecord.Controls.Add(lbNum7, 0, 8); tableTestRecord.Controls.Add(lbNum8, 0, 9); tableTestRecord.Controls.Add(lbNum9, 0, 10);
                tableTestRecord.Controls.Add(lbNum10, 0, 11);
                //tableTestRecord.Controls.Add(lbFormat1, 5, 2);tableTestRecord.SetRowSpan(lbFormat1, 10);
                tableTestRecord.Controls.Add(p1, 5, 2); tableTestRecord.SetRowSpan(p1, 10);
                //tableTestRecord.Controls.Add(lbFormat2, 8, 2);tableTestRecord.SetRowSpan(lbFormat2, 10);
                tableTestRecord.Controls.Add(p2, 8, 2); tableTestRecord.SetRowSpan(p2, 10);

                tableTestRecord.Controls.Add(las0_1[0], 1, 2); tableTestRecord.Controls.Add(las0_1[1], 1, 3); tableTestRecord.Controls.Add(las0_1[2], 1, 4);
                tableTestRecord.Controls.Add(las0_1[3], 1, 5); tableTestRecord.Controls.Add(las0_1[4], 1, 6); tableTestRecord.Controls.Add(las0_1[5], 1, 7);
                tableTestRecord.Controls.Add(las0_1[6], 1, 8); tableTestRecord.Controls.Add(las0_1[7], 1, 9); tableTestRecord.Controls.Add(las0_1[8], 1, 10);
                tableTestRecord.Controls.Add(las0_1[9], 1, 11);
                tableTestRecord.Controls.Add(las0_3[0], 3, 2); tableTestRecord.Controls.Add(las0_3[1], 3, 3); tableTestRecord.Controls.Add(las0_3[2], 3, 4);
                tableTestRecord.Controls.Add(las0_3[3], 3, 5); tableTestRecord.Controls.Add(las0_3[4], 3, 6); tableTestRecord.Controls.Add(las0_3[5], 3, 7);
                tableTestRecord.Controls.Add(las0_3[6], 3, 8); tableTestRecord.Controls.Add(las0_3[7], 3, 9); tableTestRecord.Controls.Add(las0_3[8], 3, 10);
                tableTestRecord.Controls.Add(las0_3[9], 3, 11);
                tableTestRecord.Controls.Add(las0_6[0], 6, 2); tableTestRecord.Controls.Add(las0_6[1], 6, 3); tableTestRecord.Controls.Add(las0_6[2], 6, 4);
                tableTestRecord.Controls.Add(las0_6[3], 6, 5); tableTestRecord.Controls.Add(las0_6[4], 6, 6); tableTestRecord.Controls.Add(las0_6[5], 6, 7);
                tableTestRecord.Controls.Add(las0_6[6], 6, 8); tableTestRecord.Controls.Add(las0_6[7], 6, 9); tableTestRecord.Controls.Add(las0_6[8], 6, 10);
                tableTestRecord.Controls.Add(las0_6[9], 6, 11);

                tableTestRecord.Controls.Add(las10_4[0], 4, 2); tableTestRecord.Controls.Add(las10_4[1], 4, 3); tableTestRecord.Controls.Add(las10_4[2], 4, 4);
                tableTestRecord.Controls.Add(las10_4[3], 4, 5); tableTestRecord.Controls.Add(las10_4[4], 4, 6); tableTestRecord.Controls.Add(las10_4[5], 4, 7);
                tableTestRecord.Controls.Add(las10_4[6], 4, 8); tableTestRecord.Controls.Add(las10_4[7], 4, 9); tableTestRecord.Controls.Add(las10_4[8], 4, 10);
                tableTestRecord.Controls.Add(las10_4[9], 4, 11);
                tableTestRecord.Controls.Add(las10_7[0], 7, 2); tableTestRecord.Controls.Add(las10_7[1], 7, 3); tableTestRecord.Controls.Add(las10_7[2], 7, 4);
                tableTestRecord.Controls.Add(las10_7[3], 7, 5); tableTestRecord.Controls.Add(las10_7[4], 7, 6); tableTestRecord.Controls.Add(las10_7[5], 7, 7);
                tableTestRecord.Controls.Add(las10_7[6], 7, 8); tableTestRecord.Controls.Add(las10_7[7], 7, 9); tableTestRecord.Controls.Add(las10_7[8], 7, 10);
                tableTestRecord.Controls.Add(las10_7[9], 7, 11);

                tableTestRecord.Controls.Add(las32[0], 2, 2); tableTestRecord.Controls.Add(las32[1], 2, 3); tableTestRecord.Controls.Add(las32[2], 2, 4);
                tableTestRecord.Controls.Add(las32[3], 2, 5); tableTestRecord.Controls.Add(las32[4], 2, 6); tableTestRecord.Controls.Add(las32[5], 2, 7);
                tableTestRecord.Controls.Add(las32[6], 2, 8); tableTestRecord.Controls.Add(las32[7], 2, 9); tableTestRecord.Controls.Add(las32[8], 2, 10);
                tableTestRecord.Controls.Add(las32[9], 2, 11);
            }
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.tableTestResult);
            this.groupBox7.Controls.Add(this.txtTestResult);
            this.groupBox7.Controls.Add(this.lbTestResult);
            this.groupBox7.Location = new System.Drawing.Point(0, 1400);
            this.groupBox7.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Padding = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.groupBox7.Size = new System.Drawing.Size(1000, 400);
            this.groupBox7.TabIndex = 38;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "校验结果";
            {
                {
                    // 
                    // tableTestResult
                    // 
                    //this.tableTestResult.Dock = DockStyle.Fill;
                    this.tableTestResult.Location = new System.Drawing.Point(9, 42);
                    this.tableTestResult.Margin = new System.Windows.Forms.Padding(2);
                    this.tableTestResult.Name = "tableTestResult";
                    this.tableTestResult.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;
                    this.tableTestResult.AutoScroll = true;
                    this.tableTestResult.ColumnCount = 6;
                    this.tableTestResult.RowCount = 3;
                    this.tableTestResult.Size = new System.Drawing.Size(650, 200);
                    // 
                    // lbTestResultRange
                    // 
                    this.lbTestResultRange.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultRange.Dock = DockStyle.Fill;
                    this.lbTestResultRange.Margin = new Padding(0);
                    this.lbTestResultRange.Font = new Font("微软雅黑", 9);
                    this.lbTestResultRange.Text = "测量范围\n0.0mg/dL~32.0mg/dL";
                    // 
                    // lbTestResultRange1
                    // 
                    this.lbTestResultRange1.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultRange1.Dock = DockStyle.Fill;
                    this.lbTestResultRange1.Margin = new Padding(0);
                    this.lbTestResultRange1.Font = new Font("微软雅黑", 9);
                    this.lbTestResultRange1.Text = "0.0mg/dL 校验色屏";
                    // 
                    // lbTestResultRange2
                    // 
                    this.lbTestResultRange2.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultRange2.Dock = DockStyle.Fill;
                    this.lbTestResultRange2.Margin = new Padding(0);
                    this.lbTestResultRange2.Font = new Font("微软雅黑", 9);
                    this.lbTestResultRange2.Text = "32.0mg/dL 校验色屏";
                    // 
                    // lbTestResultAcc
                    // 
                    this.lbTestResultAcc.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultAcc.Dock = DockStyle.Fill;
                    this.lbTestResultAcc.Margin = new Padding(0);
                    this.lbTestResultAcc.Font = new Font("微软雅黑", 9);
                    this.lbTestResultAcc.Text = "测量精确度\n±1.5 mg/dL";
                    // 
                    // lbTestResultAcc1
                    // 
                    this.lbTestResultAcc1.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultAcc1.Dock = DockStyle.Fill;
                    this.lbTestResultAcc1.Margin = new Padding(0);
                    this.lbTestResultAcc1.Font = new Font("微软雅黑", 9);
                    this.lbTestResultAcc1.Text = "0.0mg/dL 校验色屏";
                    // 
                    // lbTestResultAcc2
                    // 
                    this.lbTestResultAcc2.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultAcc2.Dock = DockStyle.Fill;
                    this.lbTestResultAcc2.Margin = new Padding(0);
                    this.lbTestResultAcc2.Font = new Font("微软雅黑", 9);
                    this.lbTestResultAcc2.Text = "10.0mg/dL 校验色屏";
                    // 
                    // lbTestResultCV
                    // 
                    this.lbTestResultCV.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultCV.Dock = DockStyle.Fill;
                    this.lbTestResultCV.Margin = new Padding(0);
                    this.lbTestResultCV.Font = new Font("微软雅黑", 9);
                    this.lbTestResultCV.Text = "重复性(变异系数 CV)\n<10%";
                    // 
                    // lbTestResultCV1
                    // 
                    this.lbTestResultCV1.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultCV1.Dock = DockStyle.Fill;
                    this.lbTestResultCV1.Margin = new Padding(0);
                    this.lbTestResultCV1.Font = new Font("微软雅黑", 9);
                    this.lbTestResultCV1.Text = "0.0mg/dL 校验色屏";
                    // 
                    // lbTestResultCV2
                    // 
                    this.lbTestResultCV2.TextAlign = ContentAlignment.MiddleCenter;
                    this.lbTestResultCV2.Dock = DockStyle.Fill;
                    this.lbTestResultCV2.Margin = new Padding(0);
                    this.lbTestResultCV2.Font = new Font("微软雅黑", 9);
                    this.lbTestResultCV2.Text = "10.0mg/dL 校验色屏";
                    ColumnStyle colTestResultStyle1 = new ColumnStyle(SizeType.Percent, 12F);// 其余列平均分
                    ColumnStyle colTestResultStyle2 = new ColumnStyle(SizeType.Percent, 12F);
                    ColumnStyle colTestResultStyle3 = new ColumnStyle(SizeType.Percent, 12F);
                    ColumnStyle colTestResultStyle4 = new ColumnStyle(SizeType.Percent, 12F);
                    ColumnStyle colTestResultStyle5 = new ColumnStyle(SizeType.Percent, 12F);
                    ColumnStyle colTestResultStyle6 = new ColumnStyle(SizeType.Percent, 12F);
                    tableTestResult.ColumnStyles.Add(colTestResultStyle1);
                    tableTestResult.ColumnStyles.Add(colTestResultStyle2);
                    tableTestResult.ColumnStyles.Add(colTestResultStyle3);
                    tableTestResult.ColumnStyles.Add(colTestResultStyle4);
                    tableTestResult.ColumnStyles.Add(colTestResultStyle5);
                    tableTestResult.ColumnStyles.Add(colTestResultStyle6);
                    RowStyle rowTestResultStyle0 = new RowStyle(SizeType.Absolute, 50F);
                    RowStyle rowTestResultStyle1 = new RowStyle(SizeType.Absolute, 60F);
                    tableTestResult.RowStyles.Add(rowTestResultStyle0);// 行高固定
                    tableTestResult.RowStyles.Add(rowTestResultStyle1);
                    RowStyle rowTestResultStyle2 = new RowStyle(SizeType.Absolute, 30F);// 数据行
                    tableTestResult.RowStyles.Add(rowTestResultStyle2);

                    tableTestResult.Controls.Add(lbTestResultRange, 0, 0);
                    tableTestResult.SetColumnSpan(lbTestResultRange, 2);
                    tableTestResult.Controls.Add(lbTestResultAcc, 2, 0);
                    tableTestResult.SetColumnSpan(lbTestResultAcc, 2);
                    tableTestResult.Controls.Add(lbTestResultCV, 4, 0);
                    tableTestResult.SetColumnSpan(lbTestResultCV, 2);
                    tableTestResult.Controls.Add(lbTestResultRange1, 0, 1);
                    tableTestResult.Controls.Add(lbTestResultRange2, 1, 1);
                    tableTestResult.Controls.Add(lbTestResultAcc1, 2, 1);
                    tableTestResult.Controls.Add(lbTestResultAcc2, 3, 1);
                    tableTestResult.Controls.Add(lbTestResultCV1, 4, 1);
                    tableTestResult.Controls.Add(lbTestResultCV2, 5, 1);

                    tableTestResult.Controls.Add(lasResult[0], 0, 2);
                    tableTestResult.Controls.Add(lasResult[1], 1, 2);
                    tableTestResult.Controls.Add(lasResult[2], 2, 2);
                    tableTestResult.Controls.Add(lasResult[3], 3, 2);
                    tableTestResult.Controls.Add(lasResult[4], 4, 2);
                    tableTestResult.Controls.Add(lasResult[5], 5, 2);
                }
                // 
                // txtTestResult
                // 
                this.txtTestResult.Location = new System.Drawing.Point(600, 250);
                this.txtTestResult.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
                this.txtTestResult.Name = "txtLog";
                this.txtTestResult.Size = new System.Drawing.Size(100, 20);
                this.txtTestResult.TabIndex = 0;
                // 
                // lbTestResult
                // 
                this.lbTestResult.AutoSize = true;
                this.lbTestResult.Location = new System.Drawing.Point(500, 250);
                this.lbTestResult.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
                this.lbTestResult.Name = "lbTestResult";
                this.lbTestResult.Size = new System.Drawing.Size(50, 20);
                this.lbTestResult.TabIndex = 1;
                this.lbTestResult.Text = "校验结果";
            }
            // 
            // timerBleScan
            // 
            this.timerBleScan.Interval = 1000;
            this.timerBleScan.Tick += new System.EventHandler(this.timerBleScan_Tick);
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1600, 800);
            this.Controls.Add(this.tabControlMain);
            this.Controls.Add(this.panelLog);
            this.Margin = new System.Windows.Forms.Padding(5, 4, 5, 4);
            this.Name = "Form1";
            this.Text = "YSJ-10校准程序v0.0.1";
            this.Load += new System.EventHandler(this.FormLoad);
            this.tabControlMain.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tableCaliParamView)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            this.groupBox7.ResumeLayout(false);
            this.tabControlMain.ResumeLayout(false);
            this.tabPageConnection.ResumeLayout(false);
            this.tabPageCalibration.ResumeLayout(false);
            this.tabPageTestControl.ResumeLayout(false);
            this.tabPageTestRecord.ResumeLayout(false);
            this.tabPageTestResult.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.p1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.p2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.TabControl tabControlMain;
        private System.Windows.Forms.TabPage tabPageConnection;
        private System.Windows.Forms.TabPage tabPageCalibration;
        private System.Windows.Forms.TabPage tabPageTestControl;
        private System.Windows.Forms.TabPage tabPageTestRecord;
        private System.Windows.Forms.TabPage tabPageTestResult;
        private System.Windows.Forms.Panel panelLog;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.TextBox txtLog;

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Button btnReadHisRecord;
        private System.Windows.Forms.Button btnDelE01Log;
        private System.Windows.Forms.Button btnReadE01Log;
        private System.Windows.Forms.Button btnAdd1E01LogPage;
        private System.Windows.Forms.Button btnSub1E01LogPage;
        private System.Windows.Forms.TextBox txtE01LogNowPage;
        private System.Windows.Forms.Label lbE01LogPageNum;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.TextBox txtDevName;
        private System.Windows.Forms.Button btnConn;
        private System.Windows.Forms.TextBox txtSignStress;
        private System.Windows.Forms.Button btnScan;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label lbFirmwareVer_CH;
        private System.Windows.Forms.Label lbFirmwareVer;
        private System.Windows.Forms.Label lbMacAddr;
        private System.Windows.Forms.Label lbMacAddr_CH;
        private System.Windows.Forms.Label lbSN_CH;
        private System.Windows.Forms.TextBox txtSN;
        private System.Windows.Forms.Button btnWSN;
        private System.Windows.Forms.Label lbStamp;
        private System.Windows.Forms.TextBox txtStamp;
        private System.Windows.Forms.Button btnWStamp;
        private System.Windows.Forms.CheckBox ckboxENStamp;
        private System.Windows.Forms.Label lbRemainTime;
        private System.Windows.Forms.TextBox txtRemainTime;
        private System.Windows.Forms.Label lbDevState;
        private System.Windows.Forms.Button btnLock;

        private System.Windows.Forms.Button btnAutoLock;
        private System.Windows.Forms.TextBox txtAutoLockDays;
        private System.Windows.Forms.Label lbAutoLockDays;
        private System.Windows.Forms.Button btnReadRemainTime;
        private System.Windows.Forms.Button btnMeasureOnce;

        private System.Windows.Forms.GroupBox groupBox4;
        private Button btnExTestRecord;
        private Button btnAutoTest;
        private Button btnTest32mgColorBlock;
        private Button btnTest10mgColorBlock;
        private Button btnTest0mgColorBlock;

        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Button btnEnterCalMode;
        private System.Windows.Forms.Button btnEnterWhiteCal;
        private System.Windows.Forms.Button btnEnterDarkCal;
        private System.Windows.Forms.Button btnAutoCali;

        private System.Windows.Forms.Label lbSetGPWM_CH;
        private System.Windows.Forms.Button btnAdd1GPWM;
        private System.Windows.Forms.Button btnAdd5GPWM;
        private System.Windows.Forms.TextBox txtGPWMSetShow;
        private System.Windows.Forms.Button btnSub1GPWM;
        private System.Windows.Forms.Button btnSub5GPWM;
        private System.Windows.Forms.Button btnSetGPWM;
        private System.Windows.Forms.Button btnExCalRecord;

        private System.Windows.Forms.Label lbADJRatio;
        private System.Windows.Forms.Label lbSetRatio_CH;
        private System.Windows.Forms.Button btnSub1Ratio;
        private System.Windows.Forms.Button btnSub5Ratio;
        private System.Windows.Forms.TextBox txtShowSetRatio;
        private System.Windows.Forms.Button btnAdd1Ratio;
        private System.Windows.Forms.Button btnAdd5Ratio;
        private System.Windows.Forms.Button btnSetRatio;

        //private System.Windows.Forms.DataGrid tableCaliParam;
        private System.Windows.Forms.DataGridView tableCaliParamView;
        private System.Windows.Forms.DataGridViewColumn col0;
        private System.Windows.Forms.DataGridViewColumn col1;
        private System.Windows.Forms.DataGridViewColumn col2;
        private System.Windows.Forms.DataGridViewColumn col3;
        private System.Windows.Forms.DataGridViewColumn col4;
        private System.Windows.Forms.DataGridViewColumn col5;
        private System.Windows.Forms.DataGridViewColumn col6;




        private System.Windows.Forms.Label lbGPWM_CH;
        private System.Windows.Forms.TextBox txtGPWM;
        private System.Windows.Forms.Label lbBPWM_CH;
        private System.Windows.Forms.TextBox txtBPWM;
        private System.Windows.Forms.Label lbCalState;
        private System.Windows.Forms.TextBox txtCalState;
        private System.Windows.Forms.Label lbCalResult;
        private System.Windows.Forms.TextBox txtCalResult;

        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.TableLayoutPanel tableTestRecord;
        private System.Windows.Forms.Label lbSample;
        private System.Windows.Forms.Label lbRange;
        private System.Windows.Forms.Label lbRange1;
        private System.Windows.Forms.Label lbRange2;
        private System.Windows.Forms.Label lbAcc;
        private System.Windows.Forms.Label lbAcc1;
        private System.Windows.Forms.Label lbAcc2;
        private System.Windows.Forms.Label lbAccFormula;
        private System.Windows.Forms.Label lbCV;
        private System.Windows.Forms.Label lbCV1;
        private System.Windows.Forms.Label lbCV2;
        private System.Windows.Forms.Label lbCVFormula;
        private System.Windows.Forms.Label lbNum1;
        private System.Windows.Forms.Label lbNum2;
        private System.Windows.Forms.Label lbNum3;
        private System.Windows.Forms.Label lbNum4;
        private System.Windows.Forms.Label lbNum5;
        private System.Windows.Forms.Label lbNum6;
        private System.Windows.Forms.Label lbNum7;
        private System.Windows.Forms.Label lbNum8;
        private System.Windows.Forms.Label lbNum9;
        private System.Windows.Forms.Label lbNum10;
        private System.Windows.Forms.Label lbFormat1;
        private System.Windows.Forms.Label lbFormat2;
        private System.Windows.Forms.PictureBox p1;
        private System.Windows.Forms.PictureBox p2;
        //private.System.Windows.Forms.Label[] lab;

        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.TableLayoutPanel tableTestResult;
        private System.Windows.Forms.Label lbTestResultRange;
        private System.Windows.Forms.Label lbTestResultRange1;
        private System.Windows.Forms.Label lbTestResultRange2;
        private System.Windows.Forms.Label lbTestResultAcc;
        private System.Windows.Forms.Label lbTestResultAcc1;
        private System.Windows.Forms.Label lbTestResultAcc2;
        private System.Windows.Forms.Label lbTestResultCV;
        private System.Windows.Forms.Label lbTestResultCV1;
        private System.Windows.Forms.Label lbTestResultCV2;
        private System.Windows.Forms.Label lbTestResult;
        private System.Windows.Forms.TextBox txtTestResult;
        private System.Windows.Forms.Label[] lasResult;


        private System.Windows.Forms.Timer timerBleScan;
        private System.Windows.Forms.Timer timerUpdateTime;
        // 顶部按钮风格而非菜单栏
        private System.Windows.Forms.GroupBox groupBoxLogTop;
        private System.Windows.Forms.Button btnReadHisRecordTop;
        private System.Windows.Forms.Button btnDelE01LogTop;
        private System.Windows.Forms.Button btnReadE01LogTop;
        private System.Windows.Forms.Label lbE01LogPageTop;
        private System.Windows.Forms.TextBox txtE01LogNowPageTop;
        private System.Windows.Forms.Label[] las0_1;
        private System.Windows.Forms.Label[] las0_3;
        private System.Windows.Forms.Label[] las0_6;
        private System.Windows.Forms.Label[] las10_4;
        private System.Windows.Forms.Label[] las10_7;
        private System.Windows.Forms.Label[] las32;
        private System.Windows.Forms.Label[] lasTestResult;

        //ExcelWorksheet worksheet;

        // 状态栏控件
        private System.Windows.Forms.StatusStrip statusStripMain;
        private System.Windows.Forms.ToolStripStatusLabel statusLabelUser;
        private System.Windows.Forms.ToolStripStatusLabel statusLabelConnection;
        private System.Windows.Forms.ToolStripStatusLabel statusLabelLockStatus;
        private System.Windows.Forms.ToolStripButton statusButtonLogout;
        private System.Windows.Forms.ToolStripStatusLabel statusLabelTime;
        
        // 用户信息显示控件（保留用于兼容性）
        private System.Windows.Forms.Label lblCurrentUser;
        private System.Windows.Forms.Button btnLogout;
    }
}

