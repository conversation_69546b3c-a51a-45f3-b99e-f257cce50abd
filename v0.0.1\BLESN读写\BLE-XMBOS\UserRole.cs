using System;
using System.Collections.Generic;
using System.Linq;

namespace YSJ_10Cali
{
    /// <summary>
    /// 用户角色枚举
    /// </summary>
    public enum UserRole
    {
        /// <summary>
        /// 管理员
        /// </summary>
        Admin,
        /// <summary>
        /// 校准员
        /// </summary>
        Calibrator,
        /// <summary>
        /// 检测员
        /// </summary>
        Inspector
    }

    /// <summary>
    /// 用户类
    /// </summary>
    public class User
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }
        
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }
        
        /// <summary>
        /// 用户角色
        /// </summary>
        public UserRole Role { get; set; }
        
        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; }

        public User(string username, string password, UserRole role, string displayName)
        {
            Username = username;
            Password = password;
            Role = role;
            DisplayName = displayName;
        }
    }

    /// <summary>
    /// 权限管理类
    /// </summary>
    public static class PermissionManager
    {
        /// <summary>
        /// 当前登录用户
        /// </summary>
        public static User CurrentUser { get; private set; }

        /// <summary>
        /// 用户数据库
        /// </summary>
        private static readonly List<User> _users = new List<User>
        {
            new User("admin", "123", UserRole.Admin, "管理员"),
            new User("jiaozhun", "111", UserRole.Calibrator, "校准员"),
            new User("jianyan", "222", UserRole.Inspector, "检测员")
        };

        /// <summary>
        /// 验证用户登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>验证成功返回用户对象，失败返回null</returns>
        public static User ValidateUser(string username, string password)
        {
            var user = _users.FirstOrDefault(u => 
                u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) && 
                u.Password == password);
            
            if (user != null)
            {
                CurrentUser = user;
            }
            
            return user;
        }

        /// <summary>
        /// 检查当前用户是否有指定权限
        /// </summary>
        /// <param name="requiredRole">所需角色</param>
        /// <returns>是否有权限</returns>
        public static bool HasPermission(UserRole requiredRole)
        {
            if (CurrentUser == null) return false;
            
            switch (requiredRole)
            {
                case UserRole.Admin:
                    return CurrentUser.Role == UserRole.Admin;
                case UserRole.Calibrator:
                    return CurrentUser.Role == UserRole.Admin || CurrentUser.Role == UserRole.Calibrator;
                case UserRole.Inspector:
                    return CurrentUser.Role == UserRole.Admin || CurrentUser.Role == UserRole.Inspector;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取当前用户角色
        /// </summary>
        /// <returns>当前用户角色</returns>
        public static UserRole GetCurrentUserRole()
        {
            return CurrentUser?.Role ?? UserRole.Inspector;
        }

        /// <summary>
        /// 获取当前用户显示名称
        /// </summary>
        /// <returns>当前用户显示名称</returns>
        public static string GetCurrentUserName()
        {
            return CurrentUser?.DisplayName ?? "未登录";
        }

        /// <summary>
        /// 注销当前用户
        /// </summary>
        public static void Logout()
        {
            CurrentUser = null;
        }
    }
}
