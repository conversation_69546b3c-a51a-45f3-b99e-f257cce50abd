using System;
using System.Drawing;
using System.Windows.Forms;

namespace YSJ_10Cali
{
    public partial class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblTitle;

        public LoginForm()
        {
            InitializeComponent();
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Size = new Size(350, 250);
            this.Text = "用户登录";
        }

        private void InitializeComponent()
        {
            // 标题标签
            lblTitle = new Label();
            lblTitle.Text = "YSJ-10 校准上位机";
            lblTitle.Font = new Font("Microsoft YaHei", 14, FontStyle.Bold);
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Size = new Size(300, 30);
            lblTitle.Location = new Point(25, 20);
            this.Controls.Add(lblTitle);

            // 用户名标签
            lblUsername = new Label();
            lblUsername.Text = "用户名：";
            lblUsername.Font = new Font("Microsoft YaHei", 10);
            lblUsername.Size = new Size(80, 25);
            lblUsername.Location = new Point(30, 70);
            this.Controls.Add(lblUsername);

            // 用户名输入框
            txtUsername = new TextBox();
            txtUsername.Size = new Size(200, 25);
            txtUsername.Location = new Point(110, 70);
            txtUsername.Font = new Font("Microsoft YaHei", 10);
            this.Controls.Add(txtUsername);

            // 密码标签
            lblPassword = new Label();
            lblPassword.Text = "密码：";
            lblPassword.Font = new Font("Microsoft YaHei", 10);
            lblPassword.Size = new Size(80, 25);
            lblPassword.Location = new Point(30, 110);
            this.Controls.Add(lblPassword);

            // 密码输入框
            txtPassword = new TextBox();
            txtPassword.Size = new Size(200, 25);
            txtPassword.Location = new Point(110, 110);
            txtPassword.Font = new Font("Microsoft YaHei", 10);
            txtPassword.PasswordChar = '*';
            this.Controls.Add(txtPassword);

            // 登录按钮
            btnLogin = new Button();
            btnLogin.Text = "登录";
            btnLogin.Size = new Size(80, 30);
            btnLogin.Location = new Point(110, 160);
            btnLogin.Font = new Font("Microsoft YaHei", 10);
            btnLogin.Click += BtnLogin_Click;
            this.Controls.Add(btnLogin);

            // 取消按钮
            btnCancel = new Button();
            btnCancel.Text = "取消";
            btnCancel.Size = new Size(80, 30);
            btnCancel.Location = new Point(210, 160);
            btnCancel.Font = new Font("Microsoft YaHei", 10);
            btnCancel.Click += BtnCancel_Click;
            this.Controls.Add(btnCancel);

            // 设置回车键登录
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;

            // 设置默认焦点
            txtUsername.Focus();
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Text.Trim();

            if (string.IsNullOrEmpty(username))
            {
                MessageBox.Show("请输入用户名！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                MessageBox.Show("请输入密码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return;
            }

            // 验证用户
            var user = PermissionManager.ValidateUser(username, password);
            if (user != null)
            {
                MessageBox.Show($"欢迎 {user.DisplayName}！", "登录成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("用户名或密码错误！", "登录失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPassword.Text = "";
                txtPassword.Focus();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            txtUsername.Focus();
        }
    }
}
