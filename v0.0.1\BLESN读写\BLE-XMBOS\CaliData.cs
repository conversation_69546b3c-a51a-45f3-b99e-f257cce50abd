﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Math;
namespace YSJ_10Cali   
{
    public class CaliData
    {
        const UInt32 CNT = 10;  //色板0--9
        const UInt32 SpcalCNT = 3;
        public static UInt32 scnt = 0;
        public static UInt32 wcnt = 0;

        public UInt32[] dark = new UInt32[CNT];
        public UInt32[] green = new UInt32[CNT];
        public UInt32[] blue = new UInt32[CNT];
        public UInt32[] result = new UInt32[CNT];
        public bool[] datagotten = new bool[CNT];

        public UInt32[] sdark = new UInt32[SpcalCNT];
        public UInt32[] sgreen = new UInt32[SpcalCNT];
        public UInt32[] sblue = new UInt32[SpcalCNT];
        public bool sdatagotten = false;

        public UInt32[] wdark = new UInt32[SpcalCNT];
        public UInt32[] wgreen = new UInt32[SpcalCNT];
        public UInt32[] wblue = new UInt32[SpcalCNT];
        public bool wdatagotten = false;
        public float k_val = 0;
        public float b_val = 0;
        public float corrcoef_val = 0;

        public float  b_ajust = 0.0f;
        private  float[] y1_out = new float[10];
        private  float[] y2_out = new float[10];
        //     #define VERSION_STRING          "1.0.0" 

        public  struct Calib_point
        {
           public float x_value;  /* 本设备的标准色块测量原始黄疸值 */
           public float y_value;  /* 参考设备的标准色块测量原始黄疸值 */
        } ;
        public Calib_point[] calib_Points = new Calib_point[10];

        public const float PRECISION = 0.000001f;
        public  bool CalebrateStep1(UInt32 cnt)
        {
            UInt32 aval_sum = 0;
            UInt32 bval_sum = 0;
            UInt32 cval_sum = 0;
            for(UInt32 i=0;i<cnt;i++)
            {
                aval_sum += dark[i];
                bval_sum += green[i];
                cval_sum += blue[i];
            }
            k_val = aval_sum;
            b_val = cval_sum;
            return true;
        }
      
        //       bool CalibLinear(calib_point[] calib_data, int number, float* k, float* b, float* corrcoef_val)
        public bool CalibLinear(int number)
        {
            /* 计算线性拟合相关变量 */
            int i;
            double x_sum = 0;
            double y_sum = 0;
  //          double x_mean = 0;
  //          double y_mean = 0;
            double x_square_sum = 0;
            double x_y_mul_sum = 0;

            /* 计算相关系数相关变量 */
           

            /* The number of the calibration points is little */
            if (number < 2)
            {
                return false;
            }
            for (i = 0; i < number; i++)
            {
                x_sum += calib_Points[i].x_value;
                y_sum += calib_Points[i].y_value;
                x_square_sum += calib_Points[i].x_value * calib_Points[i].x_value;
                x_y_mul_sum += calib_Points[i].x_value * calib_Points[i].y_value;
            }

            k_val =(float)((x_y_mul_sum - x_sum * y_sum / number)/(x_square_sum - x_sum * x_sum / number));
            b_val = (float)(y_sum / number - k_val * x_sum / number);

            /* 计算相关系数 */
          //  y1_out = (float*)malloc(number * sizeof(float));
           // y2_out = (float*)malloc(number * sizeof(float));
            for (i = 0; i < number; i++)
            {
                y1_out[i] = calib_Points[i].y_value;
                y2_out[i] = (k_val) * calib_Points[i].x_value + (b_val);
            }
            corrcoef_val = corrcoef(y1_out, y2_out, number);
            return true;
        }

        float corrcoef(float[] first, float[] second, int length)
        {
            float first_var, second_var;
            float numerator, denominator;
            first_var = variance(first, length);
            second_var = variance(second, length); /* z-shape */
            denominator =(float)Math.Sqrt(first_var * second_var);
            numerator = covariance(first, second, length);

            if (Math.Abs(denominator)> PRECISION)
            {
                return numerator / denominator;
            }
            else
            {
                return 0;
            }

        }

        float mean(float[] data, int length)
        {
            float sum = 0;
            int i;
            for (i = 0; i < length; i++)
            {
                sum += data[i];
            }
            return sum / length;
        }

        float variance(float[] data, int length)
        {
            float mean_val = 0;
            float sum = 0;
            int i;

            mean_val = mean(data, length);
            for (i = 0; i < length; i++)
            {
                sum += (data[i] - mean_val) * (data[i] - mean_val);
            }
            return sum / length;
        }

        float covariance(float[] first, float[] second, int length)
        {
            float sum = 0;
            int i;
            float first_mean, second_mean;
            for (i = 0; i < length; i++)
            {
                sum += first[i] * second[i];
            }
            first_mean = mean(first, length);
            second_mean = mean(second, length);
            return (sum / length - first_mean * second_mean);
        }
        public void Dispose()
        {
            for (UInt32 i = 0; i < CNT; i++)
            {
                dark[i] = 0;
                green[i] = 0;
                blue[i] = 0;
                datagotten[i] = false;
                k_val = 0;
                b_val = 0;
            }
        }
    }
    
}
