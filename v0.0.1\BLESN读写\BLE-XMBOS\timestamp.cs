﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YSJ_10Cali
{
    class timestamp
    {
		/// <summary>
		/// 取当前时间的时间戳，高并发情况下会有重复。想要解决这问题请使用加锁或其他方式。
		/// </summary>
		/// <param name="accurateToMilliseconds">是否精确到毫秒</param>
		/// <returns>返回long类型时间戳</returns>
		public static long GetTimeStamp(bool accurateToMilliseconds = false)
		{
			if (accurateToMilliseconds)
			{
				return DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
			}
			else
			{
				return DateTimeOffset.UtcNow.ToUnixTimeSeconds();
			}
		}
		/// <summary>
		/// 取指定时间的时间戳
		/// </summary>
		/// <param name="accurateToMilliseconds">是否精确到毫秒</param>
		/// <returns>返回long类型时间戳</returns>
		public static long GetTimeStamp(DateTime dateTime, bool accurateToMilliseconds = false)
		{
			if (accurateToMilliseconds)
			{
				return new DateTimeOffset(dateTime).ToUnixTimeMilliseconds();
			}
			else
			{
				return new DateTimeOffset(dateTime).ToUnixTimeSeconds();
			}
		}
		/// <summary>
		/// 指定时间戳转为时间。
		/// </summary>
		/// <param name="timeStamp">需要被反转的时间戳</param>
		/// <param name="accurateToMilliseconds">是否精确到毫秒</param>
		/// <returns>返回时间戳对应的DateTime</returns>
		public static DateTime GetTime(long timeStamp, bool accurateToMilliseconds = false)
		{
			if (accurateToMilliseconds)
			{
				return DateTimeOffset.FromUnixTimeMilliseconds(timeStamp).LocalDateTime;
			}
			else
			{
				return DateTimeOffset.FromUnixTimeSeconds(timeStamp).LocalDateTime;
			}
		}


	}
}
