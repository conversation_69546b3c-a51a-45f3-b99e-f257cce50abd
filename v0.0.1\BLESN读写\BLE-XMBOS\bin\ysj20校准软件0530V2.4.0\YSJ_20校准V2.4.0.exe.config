﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
      <section name="YSJ_20Cali.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
  </startup>
  <appSettings>
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <userSettings>
    <YSJ_20Cali.Properties.Settings>
      <setting name="C0val" serializeAs="String">
        <value>1100</value>
      </setting>
      <setting name="C1val" serializeAs="String">
        <value>2370</value>
      </setting>
      <setting name="C2val" serializeAs="String">
        <value>4500</value>
      </setting>
      <setting name="C3val" serializeAs="String">
        <value>9370</value>
      </setting>
      <setting name="C4val" serializeAs="String">
        <value>12900</value>
      </setting>
      <setting name="C5val" serializeAs="String">
        <value>12900</value>
      </setting>
      <setting name="C6val" serializeAs="String">
        <value>16830</value>
      </setting>
      <setting name="C7val" serializeAs="String">
        <value>21530</value>
      </setting>
      <setting name="C8val" serializeAs="String">
        <value>26200</value>
      </setting>
      <setting name="C9val" serializeAs="String">
        <value>27470</value>
      </setting>
      <setting name="signalstress" serializeAs="String">
        <value>4</value>
      </setting>
      <setting name="corrcoef_val" serializeAs="String">
        <value>0.9</value>
      </setting>
      <setting name="skincalratio" serializeAs="String">
        <value>10.5</value>
      </setting>
    </YSJ_20Cali.Properties.Settings>
  </userSettings>
</configuration>