﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.Annotations</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.DataAnnotations.AssociationAttribute">
      <summary>Spécifie qu'un membre d'entité représente une relation de données, telle qu'une relation de clé étrangère.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.AssociationAttribute.#ctor(System.String,System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.AssociationAttribute" />.</summary>
      <param name="name">Nom de l'association. </param>
      <param name="thisKey">Liste séparée par des virgules des noms de propriété des valeurs de clé du côté <paramref name="thisKey" /> de l'association.</param>
      <param name="otherKey">Liste séparée par des virgules des noms de propriété des valeurs de clé du côté <paramref name="otherKey" /> de l'association.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.IsForeignKey">
      <summary>Obtient ou définit une valeur qui indique si le membre d'association représente une clé étrangère.</summary>
      <returns>true si l'association représente une clé étrangère ; sinon, false.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.Name">
      <summary>Obtient le nom de l'association.</summary>
      <returns>Nom de l'association.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey">
      <summary>Obtient les noms de propriété des valeurs de clé du coté OtherKey de l'association.</summary>
      <returns>Liste séparée par des virgules des noms de propriété qui représentent les valeurs de clé du côté OtherKey de l'association.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKeyMembers">
      <summary>Obtient une collection des membres de clé individuels spécifiés dans la propriété <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey" />.</summary>
      <returns>Collection des membres de clé individuels spécifiés dans la propriété <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey" />.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey">
      <summary>Obtient les noms de propriété des valeurs de clé du coté ThisKey de l'association.</summary>
      <returns>Liste séparée par des virgules des noms de propriété qui représentent les valeurs de clé du côté ThisKey de l'association.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKeyMembers">
      <summary>Obtient une collection des membres de clé individuels spécifiés dans la propriété <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey" />.</summary>
      <returns>Collection des membres de clé individuels spécifiés dans la propriété <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey" />.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CompareAttribute">
      <summary>Fournit un attribut qui compare deux propriétés.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.CompareAttribute" />.</summary>
      <param name="otherProperty">Propriété à comparer à la propriété actuelle.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.FormatErrorMessage(System.String)">
      <summary>Applique la mise en forme à un message d'erreur en fonction du champ de données dans lequel l'erreur s'est produite.</summary>
      <returns>Message d'erreur mis en forme.</returns>
      <param name="name">Nom du champ ayant provoqué l'échec de validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.IsValid(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Détermine si un objet spécifié est valide.</summary>
      <returns>true si <paramref name="value" /> est valide ; sinon, false.</returns>
      <param name="value">Objet à valider.</param>
      <param name="validationContext">Objet qui contient des informations sur la demande de validation.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.OtherProperty">
      <summary>Obtient la propriété à comparer à la propriété actuelle.</summary>
      <returns>Autre propriété.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.OtherPropertyDisplayName">
      <summary>Obtient le nom complet de l'autre propriété.</summary>
      <returns>Nom complet de l'autre propriété.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.RequiresValidationContext">
      <summary>Obtient une valeur qui indique si l'attribut requiert un contexte de validation.</summary>
      <returns>true si l'attribut requiert un contexte de validation ; sinon, false.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute">
      <summary>Indique si une propriété participe aux contrôles d'accès concurrentiel optimiste.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute" />.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CreditCardAttribute">
      <summary>Spécifie qu'une valeur de champ de données est un numéro de carte de crédit.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CreditCardAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.CreditCardAttribute" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CreditCardAttribute.IsValid(System.Object)">
      <summary>Détermine si le nombre de cartes de crédit spécifié est valide. </summary>
      <returns>true si le numéro de carte de crédit est valide ; sinon, false.</returns>
      <param name="value">Valeur à valider.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CustomValidationAttribute">
      <summary>Spécifie une méthode de validation personnalisée utilisée pour valider une propriété ou une instance de classe.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CustomValidationAttribute.#ctor(System.Type,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.CustomValidationAttribute" />.</summary>
      <param name="validatorType">Type contenant la méthode qui exécute la validation personnalisée.</param>
      <param name="method">Méthode qui exécute la validation personnalisée.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CustomValidationAttribute.FormatErrorMessage(System.String)">
      <summary>Met en forme un message d'erreur de validation.</summary>
      <returns>Instance du message d'erreur mis en forme.</returns>
      <param name="name">Nom à inclure dans le message mis en forme.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CustomValidationAttribute.Method">
      <summary>Obtient la méthode de validation.</summary>
      <returns>Nom de la méthode de validation.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CustomValidationAttribute.ValidatorType">
      <summary>Obtient le type qui exécute la validation personnalisée.</summary>
      <returns>Type qui exécute la validation personnalisée.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DataType">
      <summary>Représente une énumération des types de données associés à des champs de données et des paramètres. </summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.CreditCard">
      <summary>Représente un numéro de carte de crédit.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Currency">
      <summary>Représente une valeur monétaire.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Custom">
      <summary>Représente un type de données personnalisé.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Date">
      <summary>Représente une valeur de date.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.DateTime">
      <summary>Représente un instant, exprimé sous la forme d'une date ou d'une heure.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Duration">
      <summary>Représente une durée continue pendant laquelle un objet existe.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.EmailAddress">
      <summary>Représente une adresse de messagerie.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Html">
      <summary>Représente un fichier HTML.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.ImageUrl">
      <summary>Représente une URL d'image.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.MultilineText">
      <summary>Représente un texte multiligne.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Password">
      <summary>Représente une valeur de mot de passe.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.PhoneNumber">
      <summary>Représente une valeur de numéro de téléphone.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.PostalCode">
      <summary>Représente un code postal.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Text">
      <summary>Représente du texte affiché.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Time">
      <summary>Représente une valeur de temps.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Upload">
      <summary>Représente le type de données de téléchargement de fichiers.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Url">
      <summary>Représente une valeur d'URL.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DataTypeAttribute">
      <summary>Spécifie le nom d'un type supplémentaire à associer à un champ de données.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.#ctor(System.ComponentModel.DataAnnotations.DataType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.DataTypeTypeAttribute" /> à l'aide du nom de type spécifié.</summary>
      <param name="dataType">Nom du type à associer au champ de données.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.DataTypeTypeAttribute" /> à l'aide du nom de modèle de champ spécifié.</summary>
      <param name="customDataType">Nom du modèle de champ personnalisé à associer au champ de données.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="customDataType" /> est null ou est une chaîne vide (""). </exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.CustomDataType">
      <summary>Obtient le nom du modèle de champ personnalisé associé au champ de données.</summary>
      <returns>Nom du modèle de champ personnalisé associé au champ de données.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.DataType">
      <summary>Obtient le type associé au champ de données.</summary>
      <returns>Une des valeurs de <see cref="T:System.ComponentModel.DataAnnotations.DataType" />.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.DisplayFormat">
      <summary>Obtient un format d'affichage de champ de données.</summary>
      <returns>Format d'affichage de champ de données.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.GetDataTypeName">
      <summary>Retourne le nom du type associé au champ de données.</summary>
      <returns>Nom du type associé au champ de données.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.IsValid(System.Object)">
      <summary>Vérifie que la valeur du champ de données est valide.</summary>
      <returns>Toujours true.</returns>
      <param name="value">Valeur de champ de données à valider.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayAttribute">
      <summary>Fournit un attribut à usage général qui vous permet de spécifier les chaînes localisables pour les types et membres de classes partielles d'entité.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.DisplayAttribute" />.</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField">
      <summary>Obtient ou définit une valeur qui indique si l'interface utilisateur doit être générée automatiquement pour afficher ce champ.</summary>
      <returns>true si l'interface utilisateur doit être générée automatiquement pour afficher ce champ ; sinon, false.</returns>
      <exception cref="T:System.InvalidOperationException">Une tentative d'obtention de la valeur de la propriété avant sa définition a été effectuée.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateFilter">
      <summary>Obtient ou définit une valeur qui indique si l'interface utilisateur du filtrage s'affiche automatiquement pour ce champ. </summary>
      <returns>true si l'interface utilisateur doit être générée automatiquement pour afficher le filtrage de ce champ ; sinon, false.</returns>
      <exception cref="T:System.InvalidOperationException">Une tentative d'obtention de la valeur de la propriété avant sa définition a été effectuée.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description">
      <summary>Obtient ou définit une valeur utilisée pour afficher une description dans l'interface utilisateur.</summary>
      <returns>Valeur utilisée pour afficher une description dans l'interface utilisateur.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetAutoGenerateField">
      <summary>Retourne la valeur de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField" />.</summary>
      <returns>Valeur de <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField" /> si la propriété a été initialisée ; sinon, null.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetAutoGenerateFilter">
      <summary>Retourne une valeur qui indique si l'interface utilisateur doit être générée automatiquement pour afficher le filtrage de ce champ. </summary>
      <returns>Valeur de <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateFilter" /> si la propriété a été initialisée ; sinon, null.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetDescription">
      <summary>Retourne la valeur de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" />.</summary>
      <returns>Description localisée si <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> a été spécifié et que la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> représente une clé de ressource ; sinon, valeur non localisée de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" />.</returns>
      <exception cref="T:System.InvalidOperationException">La propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> et la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> sont initialisées, mais une propriété statique publique qui a un nom qui correspond à la valeur <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> n'a pas pu être trouvée pour la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" />.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetGroupName">
      <summary>Retourne la valeur de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" />.</summary>
      <returns>Valeur qui sera utilisée pour le regroupement de champs dans l'interface utilisateur, si <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" /> a été initialisé ; sinon, null.Si la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> a été spécifiée et que la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" /> représente une clé de ressource, une chaîne localisée est retournée ; sinon, une chaîne non localisée est retournée.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetName">
      <summary>Retourne une valeur utilisée pour l'affichage des champs dans l'interface utilisateur.</summary>
      <returns>Chaîne localisée pour la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> lorsque la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> a été spécifiée et que la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> représente une clé de ressource ; sinon, valeur non localisée de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" />.</returns>
      <exception cref="T:System.InvalidOperationException">La propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> et la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> sont initialisées, mais une propriété statique publique qui a un nom qui correspond à la valeur <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> n'a pas pu être trouvée pour la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" />.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetOrder">
      <summary>Retourne la valeur de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order" />.</summary>
      <returns>Valeur de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order" /> si elle a été définie ; sinon, null.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetPrompt">
      <summary>Retourne la valeur de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" />.</summary>
      <returns>Obtient la chaîne localisée pour la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> si la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> a été spécifiée et si la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> représente une clé de ressource ; sinon, valeur non localisée de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" />.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetShortName">
      <summary>Retourne la valeur de la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" />.</summary>
      <returns>Chaîne localisée pour la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> si la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> a été spécifiée et si la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> représente une clé de ressource ; sinon, valeur non localisée de la propriété de valeur <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" />.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName">
      <summary>Obtient ou définit une valeur utilisée regrouper des champs dans l'interface utilisateur.</summary>
      <returns>Valeur utilisée pour regrouper des champs dans l'interface utilisateur.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name">
      <summary>Obtient ou définit une valeur utilisée pour l'affichage dans l'interface utilisateur.</summary>
      <returns>Valeur utilisée pour l'affichage dans l'interface utilisateur.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order">
      <summary>Obtient ou définit la largeur de la colonne.</summary>
      <returns>Largeur de la colonne.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt">
      <summary>Obtient ou définit une valeur qui sera utilisée pour définir le filigrane pour les invites dans l'interface utilisateur.</summary>
      <returns>Valeur qui sera utilisée pour afficher un filigrane dans l'interface utilisateur.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType">
      <summary>Obtient ou définit le type qui contient les ressources pour les propriétés <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" />, <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" />, <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> et <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" />.</summary>
      <returns>Type de la ressource qui contient les propriétés <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" />, <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" />, <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> et <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" />.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName">
      <summary>Obtient ou définit une valeur utilisée pour l'étiquette de colonne de la grille.</summary>
      <returns>Valeur utilisée pour l'étiquette de colonne de la grille.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute">
      <summary>Spécifie la colonne affichée dans la table à laquelle il est fait référence comme colonne clé étrangère.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> à l'aide de la colonne spécifiée. </summary>
      <param name="displayColumn">Nom de la colonne à utiliser comme colonne d'affichage.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> en utilisant les colonnes de tri et d'affichage spécifiées. </summary>
      <param name="displayColumn">Nom de la colonne à utiliser comme colonne d'affichage.</param>
      <param name="sortColumn">Nom de la colonne à utiliser pour le tri.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String,System.String,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> en utilisant la colonne d'affichage spécifiée et la colonne et l'ordre de tri spécifiés. </summary>
      <param name="displayColumn">Nom de la colonne à utiliser comme colonne d'affichage.</param>
      <param name="sortColumn">Nom de la colonne à utiliser pour le tri.</param>
      <param name="sortDescending">true pour trier par ordre décroissant ; sinon, false.La valeur par défaut est false.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.DisplayColumn">
      <summary>Obtient le nom de la colonne à utiliser comme champ d'affichage.</summary>
      <returns>Nom de la colonne d'affichage.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.SortColumn">
      <summary>Obtient le nom de la colonne à utiliser pour le tri.</summary>
      <returns>Nom de la colonne de tri.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.SortDescending">
      <summary>Obtient une valeur qui indique s'il faut trier par ordre croissant ou décroissant.</summary>
      <returns>true si la colonne doit être triée par ordre décroissant ; sinon, false.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayFormatAttribute">
      <summary>Spécifie la manière dont les champs de données sont affichés et mis en forme par Dynamic Data ASP.NET.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.DisplayFormatAttribute" />. </summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.ApplyFormatInEditMode">
      <summary>Obtient ou définit une valeur qui indique si la chaîne de mise en forme spécifiée par la propriété <see cref="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.DataFormatString" /> est appliquée à la valeur de champ lorsque le champ de données est en mode Édition.</summary>
      <returns>true si la chaîne de mise en forme s'applique à la valeur de champ en mode Édition ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.ConvertEmptyStringToNull">
      <summary>Obtient ou définit une valeur qui indique si les chaînes vides ("") sont converties automatiquement en valeurs null lorsque le champ de données est mis à jour dans la source de données.</summary>
      <returns>true si les chaînes vides sont converties automatiquement en null ; sinon, false.La valeur par défaut est true.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.DataFormatString">
      <summary>Obtient ou définit le format d'affichage de la valeur de champ.</summary>
      <returns>Chaîne de mise en forme qui spécifie le format d'affichage de la valeur du champ de données.La valeur par défaut est une chaîne vide (""), ce qui signifie qu'aucune mise en forme spéciale n'est appliquée à la valeur de champ.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.HtmlEncode">
      <summary>Obtient ou définit une valeur qui indique si le champ doit être encodé en HTML.</summary>
      <returns>true si le champ doit être encodé en HTML ; sinon, false.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.NullDisplayText">
      <summary>Obtient ou définit le texte affiché pour un champ lorsque la valeur du champ est null.</summary>
      <returns>Texte affiché pour un champ lorsque la valeur du champ est null.La valeur par défaut est une chaîne vide (""), ce qui signifie que cette propriété n'est pas définie.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EditableAttribute">
      <summary>Indique si un champ de données est modifiable.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EditableAttribute.#ctor(System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.EditableAttribute" />.</summary>
      <param name="allowEdit">true pour spécifier que le champ est modifiable ; sinon, false.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EditableAttribute.AllowEdit">
      <summary>Obtient une valeur qui indique si un champ est modifiable.</summary>
      <returns>true si le champ est modifiable ; sinon, false.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EditableAttribute.AllowInitialValue">
      <summary>Obtient ou définit une valeur qui indique si une valeur initiale est activée.</summary>
      <returns>true si une valeur initiale est activée ; sinon, false.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EmailAddressAttribute">
      <summary>Valide une adresse de messagerie.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EmailAddressAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.EmailAddressAttribute" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EmailAddressAttribute.IsValid(System.Object)">
      <summary>Détermine si la valeur spécifiée correspond au modèle d'une adresse de messagerie valide.</summary>
      <returns>true si la valeur spécifiée est valide ou null ; sinon, false.</returns>
      <param name="value">Valeur à valider.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute">
      <summary>Permet le mappage d'une énumération .NET Framework à une colonne de données.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.#ctor(System.Type)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute" />.</summary>
      <param name="enumType">Type de l'énumération.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.EnumType">
      <summary>Obtient ou définit le type de l'énumération.</summary>
      <returns>Type d'énumération.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.IsValid(System.Object)">
      <summary>Vérifie que la valeur du champ de données est valide.</summary>
      <returns>true si la valeur du champ de données est valide ; sinon, false.</returns>
      <param name="value">Valeur de champ de données à valider.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.FileExtensionsAttribute">
      <summary>Valide les extensions de nom de fichier.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.FileExtensionsAttribute" />.</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.Extensions">
      <summary>Obtient ou définit les extensions de nom de fichier.</summary>
      <returns>Extensions de nom de fichier, ou extensions de fichier par défaut (".png », « .jpg », « .jpeg » et « .gif ») si la propriété n'est pas définie.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.FormatErrorMessage(System.String)">
      <summary>Applique la mise en forme à un message d'erreur en fonction du champ de données dans lequel l'erreur s'est produite.</summary>
      <returns>Message d'erreur mis en forme.</returns>
      <param name="name">Nom du champ ayant provoqué l'échec de validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.IsValid(System.Object)">
      <summary>Vérifie que les extensions de nom de fichier spécifiées sont valides.</summary>
      <returns>true si l' extension de nom de fichier est valide ; sinon, false.</returns>
      <param name="value">Liste d'extensions de fichiers valides, délimitée par des virgules.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute">
      <summary>Représente un attribut utilisé pour spécifier le comportement de filtrage pour une colonne.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> à l'aide de l'indication de filtrage de l'interface utilisateur.</summary>
      <param name="filterUIHint">Nom du contrôle à utiliser pour le filtrage.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> à l'aide de l'indication de filtrage de l'interface utilisateur et du nom de la couche de présentation.</summary>
      <param name="filterUIHint">Nom du contrôle à utiliser pour le filtrage.</param>
      <param name="presentationLayer">Nom de la couche présentation qui prend en charge ce contrôle.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String,System.String,System.Object[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> à l'aide de l'indication de filtrage de l'interface utilisateur, du nom de la couche de présentation et des paramètres de contrôle.</summary>
      <param name="filterUIHint">Nom du contrôle à utiliser pour le filtrage.</param>
      <param name="presentationLayer">Nom de la couche présentation qui prend en charge ce contrôle.</param>
      <param name="controlParameters">Liste des paramètres pour le contrôle.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.ControlParameters">
      <summary>Obtient les paires nom/valeur utilisées comme paramètres dans le constructeur du contrôle.</summary>
      <returns>Paires nom/valeur utilisées comme paramètres dans le constructeur du contrôle.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.Equals(System.Object)">
      <summary>Retourne une valeur qui indique si cette instance d'attribut est égale à un objet spécifié.</summary>
      <returns>True si l'objet passé est égal à cette instance d'attribut ; sinon, false.</returns>
      <param name="obj">Instance d'objet à comparer avec cette instance d'attribut.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.FilterUIHint">
      <summary>Obtient le nom du contrôle à utiliser pour le filtrage.</summary>
      <returns>Nom du contrôle à utiliser pour le filtrage.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.GetHashCode">
      <summary>Retourne le code de hachage de cette instance d'attribut.</summary>
      <returns>Code de hachage de cette instance d'attribut.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.PresentationLayer">
      <summary>Obtient le nom de la couche de présentation qui prend en charge ce contrôle.</summary>
      <returns>Nom de la couche présentation qui prend en charge ce contrôle.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.IValidatableObject">
      <summary>Offre un moyen d'invalider un objet.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.IValidatableObject.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Détermine si l'objet spécifié est valide.</summary>
      <returns>Collection qui contient des informations de validations ayant échoué.</returns>
      <param name="validationContext">Contexte de validation.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.KeyAttribute">
      <summary>Dénote une ou plusieurs propriétés qui identifient une entité de manière unique.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.KeyAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.KeyAttribute" />.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute">
      <summary>Spécifie la longueur maximale du tableau ou des données de type chaîne autorisée dans une propriété.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute" /> en fonction du paramètre <paramref name="length" />.</summary>
      <param name="length">Longueur maximale autorisée du tableau ou des données de type chaîne.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.FormatErrorMessage(System.String)">
      <summary>Applique une mise en forme à un message d'erreur spécifié.</summary>
      <returns>Chaîne localisée pour décrire la longueur acceptable maximale.</returns>
      <param name="name">Nom à inclure dans la chaîne mise en forme.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.IsValid(System.Object)">
      <summary>Détermine si un objet spécifié est valide.</summary>
      <returns>true si la valeur est null ou inférieure ou égale à la longueur maximale spécifiée, sinon, false.</returns>
      <param name="value">Objet à valider.</param>
      <exception cref="Sytem.InvalidOperationException">La longueur est zéro ou moins que moins un.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.MaxLengthAttribute.Length">
      <summary>Obtient la longueur maximale autorisée du tableau ou des données de type chaîne.</summary>
      <returns>Longueur maximale autorisée du tableau ou des données de type chaîne.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.MinLengthAttribute">
      <summary>Spécifie la longueur minimale du tableau ou des données de type chaîne autorisée dans une propriété.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.MinLengthAttribute" />.</summary>
      <param name="length">Longueur du tableau ou des données de type chaîne.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.FormatErrorMessage(System.String)">
      <summary>Applique une mise en forme à un message d'erreur spécifié.</summary>
      <returns>Chaîne localisée pour décrire la longueur acceptable minimale.</returns>
      <param name="name">Nom à inclure dans la chaîne mise en forme.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.IsValid(System.Object)">
      <summary>Détermine si un objet spécifié est valide.</summary>
      <returns>true si l'objet spécifié est valide ; sinon false.</returns>
      <param name="value">Objet à valider.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.MinLengthAttribute.Length">
      <summary>Obtient ou définit la longueur minimale autorisée des données du tableau ou de type chaîne.</summary>
      <returns>Longueur minimale autorisée du tableau ou des données de type chaîne.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.PhoneAttribute">
      <summary>Spécifie qu'une valeur de champ de données est un numéro de téléphone de format correct qui utilise une expression régulière pour les numéros de téléphone.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.PhoneAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.PhoneAttribute" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.PhoneAttribute.IsValid(System.Object)">
      <summary>Détermine si le numéro de téléphone spécifié est dans un format de numéro de téléphone valide. </summary>
      <returns>true si le numéro de téléphone est valide ; sinon, false.</returns>
      <param name="value">Valeur à valider.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RangeAttribute">
      <summary>Spécifie les contraintes de plages numériques pour la valeur d'un champ de données. </summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Double,System.Double)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> à l'aide des valeurs minimale et maximale spécifiées. </summary>
      <param name="minimum">Spécifie la valeur minimale autorisée pour la valeur du champ de données.</param>
      <param name="maximum">Spécifie la valeur maximale autorisée pour la valeur du champ de données.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Int32,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> à l'aide des valeurs minimale et maximale spécifiées.</summary>
      <param name="minimum">Spécifie la valeur minimale autorisée pour la valeur du champ de données.</param>
      <param name="maximum">Spécifie la valeur maximale autorisée pour la valeur du champ de données.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Type,System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> à l'aide des valeurs minimale et maximale spécifiées et du type spécifié.</summary>
      <param name="type">Spécifie le type de l'objet à tester.</param>
      <param name="minimum">Spécifie la valeur minimale autorisée pour la valeur du champ de données.</param>
      <param name="maximum">Spécifie la valeur maximale autorisée pour la valeur du champ de données.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> a la valeur null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.FormatErrorMessage(System.String)">
      <summary>Met en forme le message d'erreur affiché en cas d'échec de la validation de la plage.</summary>
      <returns>Message d'erreur mis en forme.</returns>
      <param name="name">Nom du champ ayant provoqué l'échec de validation. </param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.IsValid(System.Object)">
      <summary>Vérifie que la valeur du champ de données est dans la plage spécifiée.</summary>
      <returns>true si la valeur spécifiée se situe dans la plage ; sinon false.</returns>
      <param name="value">Valeur de champ de données à valider.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">La valeur du champ de données était en dehors de la plage autorisée.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.Maximum">
      <summary>Obtient la valeur maximale autorisée pour le champ.</summary>
      <returns>Valeur maximale autorisée pour le champ de données.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.Minimum">
      <summary>Obtient la valeur minimale autorisée pour le champ.</summary>
      <returns>Valeur minimale autorisée pour le champ de données.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.OperandType">
      <summary>Obtient le type du champ de données dont la valeur doit être validée.</summary>
      <returns>Type du champ de données dont la valeur doit être validée.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RegularExpressionAttribute">
      <summary>Spécifie qu'une valeur de champ de données dans Dynamic Data ASP.NET doit correspondre à l'expression régulière spécifiée.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.RegularExpressionAttribute" />.</summary>
      <param name="pattern">Expression régulière utilisée pour valider la valeur du champ de données. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pattern" /> a la valeur null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.FormatErrorMessage(System.String)">
      <summary>Met en forme le message d'erreur à afficher en cas d'échec de validation de l'expression régulière.</summary>
      <returns>Message d'erreur mis en forme.</returns>
      <param name="name">Nom du champ ayant provoqué l'échec de validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.IsValid(System.Object)">
      <summary>Vérifie si la valeur entrée par l'utilisateur correspond au modèle d'expression régulière. </summary>
      <returns>true si la validation réussit ; sinon, false.</returns>
      <param name="value">Valeur de champ de données à valider.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">La valeur du champ de données ne correspondait pas au modèle d'expression régulière.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.Pattern">
      <summary>Obtient le modèle d'expression régulière.</summary>
      <returns>Modèle pour lequel établir une correspondance.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RequiredAttribute">
      <summary>Spécifie qu'une valeur de champ de données est requise.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RequiredAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.RequiredAttribute" />.</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RequiredAttribute.AllowEmptyStrings">
      <summary>Obtient ou définit une valeur qui indique si une chaîne vide est autorisée.</summary>
      <returns>true si une chaîne vide est autorisée ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RequiredAttribute.IsValid(System.Object)">
      <summary>Vérifie que la valeur du champ de données requis n'est pas vide.</summary>
      <returns>true si la validation réussit ; sinon, false.</returns>
      <param name="value">Valeur de champ de données à valider.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">La valeur du champ de données était null.</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute">
      <summary>Spécifie si une classe ou une colonne de données utilise la structure.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.#ctor(System.Boolean)">
      <summary>Initialise une nouvelle instance de <see cref="T:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute" /> à l'aide de la propriété <see cref="P:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.Scaffold" />.</summary>
      <param name="scaffold">Valeur qui spécifie si la structure est activée.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.Scaffold">
      <summary>Obtient ou définit la valeur qui spécifie si la structure est activée.</summary>
      <returns>true si la structure est activée ; sinon, false.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.StringLengthAttribute">
      <summary>Spécifie la longueur minimale et maximale des caractères autorisés dans un champ de données.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.StringLengthAttribute" /> en utilisant une longueur maximale spécifiée.</summary>
      <param name="maximumLength">Longueur maximale d'une chaîne. </param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.FormatErrorMessage(System.String)">
      <summary>Applique une mise en forme à un message d'erreur spécifié.</summary>
      <returns>Message d'erreur mis en forme.</returns>
      <param name="name">Nom du champ ayant provoqué l'échec de validation.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumLength" /> est négatif. ou<paramref name="maximumLength" /> est inférieur à <paramref name="minimumLength" />.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.IsValid(System.Object)">
      <summary>Détermine si un objet spécifié est valide.</summary>
      <returns>true si l'objet spécifié est valide ; sinon false.</returns>
      <param name="value">Objet à valider.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumLength" /> est négatif.ou<paramref name="maximumLength" /> est inférieur à <see cref="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MinimumLength" />.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MaximumLength">
      <summary>Obtient ou définit la longueur maximale d'une chaîne.</summary>
      <returns>Longueur maximale d'une chaîne. </returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MinimumLength">
      <summary>Obtient ou définit la longueur minimale d'une chaîne.</summary>
      <returns>Longueur minimale d'une chaîne.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.TimestampAttribute">
      <summary>Spécifie le type de données de la colonne en tant que version de colonne.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.TimestampAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.TimestampAttribute" />.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.UIHintAttribute">
      <summary>Spécifie le modèle ou le contrôle utilisateur utilisé par Dynamic Data pour afficher un champ de données. </summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> en utilisant un nom de contrôle spécifié par l'utilisateur. </summary>
      <param name="uiHint">Contrôle utilisateur à utiliser pour afficher le champ de données. </param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> en utilisant le contrôle utilisateur et la couche de présentation spécifiés. </summary>
      <param name="uiHint">Contrôle utilisateur (modèle de champ) à utiliser pour afficher le champ de données.</param>
      <param name="presentationLayer">Couche de présentation qui utilise la classe.Peut avoir la valeur "HTML", "Silverlight", "WPF" ou "WinForms".</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String,System.String,System.Object[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> en utilisant le contrôle utilisateur, la couche de présentation et les paramètres de contrôle spécifiés.</summary>
      <param name="uiHint">Contrôle utilisateur (modèle de champ) à utiliser pour afficher le champ de données.</param>
      <param name="presentationLayer">Couche de présentation qui utilise la classe.Peut avoir la valeur "HTML", "Silverlight", "WPF" ou "WinForms".</param>
      <param name="controlParameters">Objet à utiliser pour extraire des valeurs de toute source de données. </param>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters" /> est null ou est une clé de contrainte.ouLa valeur de <see cref="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters" /> n'est pas une chaîne. </exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters">
      <summary>Obtient ou définit l'objet <see cref="T:System.Web.DynamicData.DynamicControlParameter" /> à utiliser pour extraire des valeurs de toute source de données.</summary>
      <returns>Collection de paires clé-valeur. </returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.Equals(System.Object)">
      <summary>Obtient une valeur qui indique si cette instance équivaut à l'objet spécifié.</summary>
      <returns>true si l'objet spécifié équivaut à cette instance ; sinon, false.</returns>
      <param name="obj">Objet à comparer à cette instance ou référence null.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.GetHashCode">
      <summary>Obtient le code de hachage de l'instance actuelle de l'attribut.</summary>
      <returns>Code de hachage de l'instance de l'attribut.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.PresentationLayer">
      <summary>Obtient ou définit la couche de présentation qui utilise la classe <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" />. </summary>
      <returns>Couche de présentation utilisée par cette classe.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.UIHint">
      <summary>Obtient ou définit le nom du modèle de champ à utiliser pour afficher le champ de données.</summary>
      <returns>Nom du modèle de champ qui affiche le champ de données.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.UrlAttribute">
      <summary>Fournit la validation de l'URL.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UrlAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.UrlAttribute" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UrlAttribute.IsValid(System.Object)">
      <summary>Valide le format de l'URL spécifiée.</summary>
      <returns>true si le format d'URL est valide ou null ; sinon, false.</returns>
      <param name="value">URL à valider.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationAttribute">
      <summary>Sert de classe de base pour tous les attributs de validation.</summary>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">Les propriétés <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType" /> et <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceName" /> pour le message d'erreur localisé sont définies en même temps que le message d'erreur de propriété <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessage" /> non localisé.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor(System.Func{System.String})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> à l'aide de la fonction qui autorise l'accès aux ressources de validation.</summary>
      <param name="errorMessageAccessor">Fonction qui autorise l'accès aux ressources de validation.</param>
      <exception cref="T:System:ArgumentNullException">
        <paramref name="errorMessageAccessor" /> a la valeur null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> à l'aide du message d'erreur à associer à un contrôle de validation.</summary>
      <param name="errorMessage">Message d'erreur à associer à un contrôle de validation.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessage">
      <summary>Obtient ou définit un message d'erreur à associer à un contrôle de validation si la validation échoue.</summary>
      <returns>Message d'erreur associé au contrôle de validation.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceName">
      <summary>Obtient ou définit le nom de la ressource de message d'erreur à utiliser pour rechercher la valeur de la propriété <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType" /> si la validation échoue.</summary>
      <returns>Ressource de message d'erreur associée à un contrôle de validation.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType">
      <summary>Obtient ou définit le type de ressource à utiliser pour la recherche de message d'erreur si une validation échoue.</summary>
      <returns>Type de message d'erreur associé à un contrôle de validation.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageString">
      <summary>Obtient le message d'erreur de validation localisé.</summary>
      <returns>Message d'erreur de validation localisé.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.FormatErrorMessage(System.String)">
      <summary>Applique la mise en forme à un message d'erreur en fonction du champ de données dans lequel l'erreur s'est produite. </summary>
      <returns>Instance du message d'erreur mis en forme.</returns>
      <param name="name">Nom à inclure dans le message mis en forme.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.GetValidationResult(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Vérifie si la valeur spécifiée est valide en tenant compte de l'attribut de validation actuel.</summary>
      <returns>Instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" />. </returns>
      <param name="value">Valeur à valider.</param>
      <param name="validationContext">Informations de contexte concernant l'opération de validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(System.Object)">
      <summary>Détermine si la valeur spécifiée de l'objet est valide. </summary>
      <returns>true si la valeur spécifiée est valide ; sinon, false.</returns>
      <param name="value">Valeur de l'objet à valider. </param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Valide la valeur spécifiée en tenant compte de l'attribut de validation actuel.</summary>
      <returns>Instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" />. </returns>
      <param name="value">Valeur à valider.</param>
      <param name="validationContext">Informations de contexte concernant l'opération de validation.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.RequiresValidationContext">
      <summary>Obtient une valeur qui indique si l'attribut requiert un contexte de validation.</summary>
      <returns>true si l'attribut requiert un contexte de validation ; sinon, false.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.Validate(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Valide l'objet spécifié.</summary>
      <param name="value">Objet à valider.</param>
      <param name="validationContext">Objet <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> qui décrit le contexte dans lequel les contrôles de validation sont effectués.Ce paramètre ne peut pas être null.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">Échec de la validation.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.Validate(System.Object,System.String)">
      <summary>Valide l'objet spécifié.</summary>
      <param name="value">Valeur de l'objet à valider.</param>
      <param name="name">Nom à inclure dans le message d'erreur.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">
        <paramref name="value" /> n'est pas valide.</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationContext">
      <summary>Décrit le contexte dans lequel un contrôle de validation est exécuté.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> à l'aide de l'instance d'objet spécifiée</summary>
      <param name="instance">Instance de l'objet à valider.Ne peut pas être null.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object,System.Collections.Generic.IDictionary{System.Object,System.Object})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> à l'aide de l'objet spécifié et d'un conteneur des propriétés facultatif.</summary>
      <param name="instance">Instance de l'objet à valider.Ne peut pas être null</param>
      <param name="items">Jeu facultatif de paires clé/valeur à mettre à disposition des consommateurs.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object,System.IServiceProvider,System.Collections.Generic.IDictionary{System.Object,System.Object})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> à l'aide du fournisseur de services et du dictionnaire des consommateurs du service. </summary>
      <param name="instance">Objet à valider.Ce paramètre est obligatoire.</param>
      <param name="serviceProvider">Objet qui implémente l'interface <see cref="T:System.IServiceProvider" />.Ce paramètre est optionnel.</param>
      <param name="items">Dictionnaire de paires clé/valeur à mettre à disposition des consommateurs du service.Ce paramètre est optionnel.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.DisplayName">
      <summary>Obtient ou définit le nom du membre à valider. </summary>
      <returns>Nom du membre à valider. </returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.GetService(System.Type)">
      <summary>Retourne le service qui assure la validation personnalisée.</summary>
      <returns>Instance du service ou null si le service n'est pas disponible.</returns>
      <param name="serviceType">Type du service à utiliser pour la validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.InitializeServiceProvider(System.Func{System.Type,System.Object})">
      <summary>Initialise le <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> à l'aide d'un fournisseur de services qui peut retourner des instances de service par type quand GetService est appelée.</summary>
      <param name="serviceProvider">Fournisseur de services.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.Items">
      <summary>Obtient le dictionnaire de paires clé/valeur associé à ce contexte.</summary>
      <returns>Dictionnaire de paires clé/valeur pour ce contexte.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.MemberName">
      <summary>Obtient ou définit le nom du membre à valider. </summary>
      <returns>Nom du membre à valider. </returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.ObjectInstance">
      <summary>Obtient l'objet à valider.</summary>
      <returns>Objet à valider.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.ObjectType">
      <summary>Obtient le type de l'objet à valider.</summary>
      <returns>Type de l'objet à valider.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationException">
      <summary>Représente l'exception qui se produit pendant le validation d'un champ de données lorsque la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> est utilisée. </summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> avec un message d'erreur généré par le système.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.ComponentModel.DataAnnotations.ValidationResult,System.ComponentModel.DataAnnotations.ValidationAttribute,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> à l'aide d'un résultat de validation, d'un attribut de validation et de la valeur de l'exception en cours.</summary>
      <param name="validationResult">Liste des résultats de validation.</param>
      <param name="validatingAttribute">Attribut qui a provoqué l'exception actuelle.</param>
      <param name="value">Valeur de l'objet qui a fait en sorte que l'attribut déclenche l'erreur de validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> avec un message d'erreur spécifié.</summary>
      <param name="message">Message spécifié qui indique l'erreur.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String,System.ComponentModel.DataAnnotations.ValidationAttribute,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> avec un message d'erreur spécifié, un attribut de validation et la valeur de l'exception actuelle.</summary>
      <param name="errorMessage">Message qui indique l'erreur.</param>
      <param name="validatingAttribute">Attribut qui a provoqué l'exception actuelle.</param>
      <param name="value">Valeur de l'objet qui a fait en sorte que l'attribut déclenche l'erreur de validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> avec un message d'erreur spécifié et une collection d'instances d'exceptions internes.</summary>
      <param name="message">Message d'erreur. </param>
      <param name="innerException">Collection d'exceptions de validation.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationAttribute">
      <summary>Obtient l'instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> qui a déclenché cette exception.</summary>
      <returns>Instance du type d'attribut de validation qui a déclenché cette exception.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult">
      <summary>Obtient l'instance <see cref="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult" /> qui décrit l'erreur de validation.</summary>
      <returns>Instance <see cref="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult" /> qui décrit l'erreur de validation.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.Value">
      <summary>Obtient la valeur de l'objet qui fait en sorte que la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> déclenche cette exception.</summary>
      <returns>Valeur de l'objet qui a fait en sorte que la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> déclenche l'erreur de validation.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationResult">
      <summary>Représente un conteneur pour les résultats d'une demande de validation.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.ComponentModel.DataAnnotations.ValidationResult)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> à l'aide d'un objet <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" />.</summary>
      <param name="validationResult">Objet résultat de validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> en utilisant un message d'erreur spécifié.</summary>
      <param name="errorMessage">Message d'erreur.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> à l'aide d'un message d'erreur et d'une liste des membres présentant des erreurs de validation.</summary>
      <param name="errorMessage">Message d'erreur.</param>
      <param name="memberNames">Liste des noms de membre présentant des erreurs de validation.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationResult.ErrorMessage">
      <summary>Obtient le message d'erreur pour la validation.</summary>
      <returns>Message d'erreur pour la validation.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationResult.MemberNames">
      <summary>Obtient la collection des noms de membre qui indiquent quels champs présentent des erreurs de validation.</summary>
      <returns>Collection des noms de membre qui indiquent quels champs présentent des erreurs de validation.</returns>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.ValidationResult.Success">
      <summary>Représente la réussite de la validation (true si la validation a réussi ; sinon, false).</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.ToString">
      <summary>Retourne une chaîne représentant le résultat actuel de la validation.</summary>
      <returns>Résultat actuel de la validation.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Validator">
      <summary>Définit une classe d'assistance qui peut être utilisée pour valider des objets, des propriétés et des méthodes lorsqu'elle est incluse dans leurs attributs <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> associés.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult})">
      <summary>Détermine si l'objet spécifié est valide à l'aide du contexte de validation et de la collection des résultats de validation.</summary>
      <returns>true si l'objet est valide ; sinon, false.</returns>
      <param name="instance">Objet à valider.</param>
      <param name="validationContext">Contexte qui décrit l'objet à valider.</param>
      <param name="validationResults">Collection destinée à contenir les validations ayant échoué.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> a la valeur null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult},System.Boolean)">
      <summary>Détermine si l'objet spécifié est valide à l'aide du contexte de validation, de la collection des résultats de validation et d'une valeur qui spécifie s'il faut valider toutes les propriétés.</summary>
      <returns>true si l'objet est valide ; sinon, false.</returns>
      <param name="instance">Objet à valider.</param>
      <param name="validationContext">Contexte qui décrit l'objet à valider.</param>
      <param name="validationResults">Collection destinée à contenir les validations ayant échoué.</param>
      <param name="validateAllProperties">true pour valider toutes les propriétés ; si false, seuls les attributs requis sont validés.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> a la valeur null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateProperty(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult})">
      <summary>Valide la propriété.</summary>
      <returns>true si la propriété est valide ; sinon, false.</returns>
      <param name="value">Valeur à valider.</param>
      <param name="validationContext">Contexte qui décrit la propriété à valider.</param>
      <param name="validationResults">Collection destinée à contenir les validations ayant échoué. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ne peut pas être assignée à la propriété.ou<paramref name="value " />est null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateValue(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult},System.Collections.Generic.IEnumerable{System.ComponentModel.DataAnnotations.ValidationAttribute})">
      <summary>Retourne une valeur qui indique si la valeur spécifiée est valide avec les attributs spécifiés.</summary>
      <returns>true si l'objet est valide ; sinon, false.</returns>
      <param name="value">Valeur à valider.</param>
      <param name="validationContext">Contexte qui décrit l'objet à valider.</param>
      <param name="validationResults">Collection qui contient les validations ayant échoué. </param>
      <param name="validationAttributes">Attributs de validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Détermine si l'objet spécifié est valide à l'aide du contexte de validation.</summary>
      <param name="instance">Objet à valider.</param>
      <param name="validationContext">Contexte qui décrit l'objet à valider.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">L'objet n'est pas valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> a la valeur null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Boolean)">
      <summary>Détermine si l'objet spécifié est valide à l'aide du contexte de validation et d'une valeur qui spécifie s'il faut valider toutes les propriétés.</summary>
      <param name="instance">Objet à valider.</param>
      <param name="validationContext">Contexte qui décrit l'objet à valider.</param>
      <param name="validateAllProperties">true pour valider toutes les propriétés ; sinon, false.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">
        <paramref name="instance" /> n'est pas valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> a la valeur null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateProperty(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Valide la propriété.</summary>
      <param name="value">Valeur à valider.</param>
      <param name="validationContext">Contexte qui décrit la propriété à valider.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ne peut pas être assignée à la propriété.</exception>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">Le paramètre <paramref name="value" /> n'est pas valide.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateValue(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.IEnumerable{System.ComponentModel.DataAnnotations.ValidationAttribute})">
      <summary>Valide les attributs spécifiés.</summary>
      <param name="value">Valeur à valider.</param>
      <param name="validationContext">Contexte qui décrit l'objet à valider.</param>
      <param name="validationAttributes">Attributs de validation.</param>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="validationContext" /> est null.</exception>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">Le paramètre <paramref name="value" /> ne valide pas avec le paramètre <paramref name="validationAttributes" />.</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute">
      <summary>Représente la colonne de base de données à laquelle une propriété est mappée.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute" />.</summary>
      <param name="name">Nom de la colonne à laquelle la propriété est mappée.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.Name">
      <summary>Obtient le nom de la colonne à laquelle la propriété est mappée.</summary>
      <returns>Nom de la colonne à laquelle la propriété est mappée.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.Order">
      <summary>Obtient ou définit l'ordre de base zéro de la colonne à laquelle la propriété est mappée.</summary>
      <returns>Ordre de la colonne.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.TypeName">
      <summary>Obtient ou définit le type de données spécifique du fournisseur de bases de données de la colonne à laquelle la propriété est mappée.</summary>
      <returns>Type de données spécifique du fournisseur de bases de données de la colonne à laquelle la propriété est mappée.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute">
      <summary>Dénote que la classe est un type complexe.Les types complexes sont les propriétés non scalaires des types d'entités qui permettent d'organiser les propriétés scalaires au sein des entités.Les types complexes n'ont pas de clés et ne peuvent pas être gérés par l'Entity Framework, mis à part l'objet parent.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute" />.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute">
      <summary>Indique comment la base de données génère les valeurs d'une propriété.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute.#ctor(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute" />.</summary>
      <param name="databaseGeneratedOption">Option générée par la base de données.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute.DatabaseGeneratedOption">
      <summary>Obtient ou définit le modèle utilisé pour générer des valeurs pour la propriété de la base de données.</summary>
      <returns>Option générée par la base de données.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption">
      <summary>Représente le modèle utilisé pour générer des valeurs pour une propriété dans la base de données.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Computed">
      <summary>La base de données génère une valeur lorsqu'une ligne est insérée ou mise à jour.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity">
      <summary>La base de données génère une valeur lorsqu'une ligne est insérée.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.None">
      <summary>La base de données ne génère pas de valeurs.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute">
      <summary>Dénote une propriété utilisée comme une clé étrangère dans une relation.L'annotation peut être placée sur la propriété de clé étrangère et spécifier le nom de la propriété de navigation associée, ou bien placée sur une propriété de navigation et spécifier le nom de la clé étrangère associée.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute" />.</summary>
      <param name="name">Si vous ajoutez l'attribut ForeigKey à une propriété de clé étrangère, vous devez spécifier le nom de la propriété de navigation associée.Si vous ajoutez l'attribut ForeigKey à une propriété de navigation, vous devez spécifier le(s) nom(s) de la (des) clé(s) étrangère(s) associée(s).Si une propriété de navigation comporte plusieurs clés étrangères, utilisez une virgule pour séparer la liste des noms de clé étrangère.Pour plus d'informations, consultez Annotations de données Code First.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute.Name">
      <summary>Si vous ajoutez l'attribut ForeigKey à une propriété de clé étrangère, vous devez spécifier le nom de la propriété de navigation associée.Si vous ajoutez l'attribut ForeigKey à une propriété de navigation, vous devez spécifier le(s) nom(s) de la (des) clé(s) étrangère(s) associée(s).Si une propriété de navigation comporte plusieurs clés étrangères, utilisez une virgule pour séparer la liste des noms de clé étrangère.Pour plus d'informations, consultez Annotations de données Code First.</summary>
      <returns>Nom de la propriété de navigation associée ou de la propriété de clé étrangère associée.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute">
      <summary>Spécifie l'inverse d'une propriété de navigation qui représente l'autre terminaison de la même relation.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute" /> à l'aide de la propriété spécifiée.</summary>
      <param name="property">Propriété de navigation représentant l'autre extrémité de la même relation.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute.Property">
      <summary>Gets the navigation property representing the other end of the same relationship.</summary>
      <returns>Propriété de l'attribut.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute">
      <summary>Dénote qu'une propriété ou classe doit être exclue du mappage de base de données.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute" />.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.TableAttribute">
      <summary>Spécifie la table de base de données à laquelle une classe est mappée.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.TableAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataAnnotations.Schema.TableAttribute" /> à l'aide du nom de la table spécifié.</summary>
      <param name="name">Nom de la table à laquelle la classe est mappée.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.TableAttribute.Name">
      <summary>Obtient le nom de la table à laquelle la classe est mappée.</summary>
      <returns>Nom de la table à laquelle la classe est mappée.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.TableAttribute.Schema">
      <summary>Obtient ou définit le schéma de la table auquel la classe est mappée.</summary>
      <returns>Schéma de la table à laquelle la classe est mappée.</returns>
    </member>
  </members>
</doc>